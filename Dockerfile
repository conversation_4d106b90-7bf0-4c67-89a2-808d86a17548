# Build stage
FROM 058264490086.dkr.ecr.ap-northeast-1.amazonaws.com/base-rust:latest as builder

WORKDIR /usr/src/app

RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

COPY Cargo.toml Cargo.lock ./

COPY src/ ./src/

RUN cargo build --release

FROM 058264490086.dkr.ecr.ap-northeast-1.amazonaws.com/base-debian:bookworm-slim

WORKDIR /app

RUN apt-get update && apt-get install -y \
    ca-certificates \
    libssl3 \
    wget \
    && rm -rf /var/lib/apt/lists/*

RUN wget https://truststore.pki.rds.amazonaws.com/global/global-bundle.pem

COPY --from=builder /usr/src/app/target/release/ /app/