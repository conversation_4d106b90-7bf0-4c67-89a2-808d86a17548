use print_meme_collector_sol::{
    config::APP_CONFIG, db::common::setup_mongodb,
    service::collect_curve_event::collect_curve_event, utils::init_standard_tracing,
};

use axum::{routing::get, Router};

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    dotenv::dotenv().ok();
    init_standard_tracing(env!("CARGO_CRATE_NAME"));
    setup_mongodb(&APP_CONFIG.mongodb_uri).await;

    let event_collector = tokio::spawn(async move {
        let result = collect_curve_event().await;
        if let Err(e) = &result {
            tracing::error!("Event collector error: {}", e);
            panic!("Event collector error: {}", e)
        }
        result
    });

    let http_server = tokio::spawn(async {
        let app = Router::new().route("/api/v1/health", get(|| async { "Hello, World!" }));

        let listener = tokio::net::TcpListener::bind("0.0.0.0:8888").await.unwrap();
        axum::serve(listener, app).await.unwrap();
        Ok::<_, anyhow::Error>(())
    });

    let (event_result, http_result) = tokio::try_join!(event_collector, http_server)?;

    event_result?;
    http_result?;

    Ok(())
}
