use std::str::FromStr;

use print_meme_collector_sol::{
    config::APP_CONFIG, db::common::setup_mongodb,
    service::complete_bonding_curve::create_clmm_pool, utils::init_standard_tracing,
};
use solana_sdk::pubkey::Pubkey;

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    dotenv::dotenv().ok();
    init_standard_tracing(env!("CARGO_CRATE_NAME"));
    setup_mongodb(&APP_CONFIG.mongodb_uri).await;

    match create_clmm_pool(
        Pubkey::from_str("So11111111111111111111111111111111111111112")?,
        Pubkey::from_str("7DkonXdVG4LawJoagEwfyvCZvLN5KMJePhhwJyBm6EUA")?,
        // 258625000.0,
        // 800_000_000,
        25.0,
        800_000,
        800_000 * 25,
    )
    .await
    {
        Ok(_) => println!("Pool created successfully"),
        Err(e) => println!("Error: {:?}", e),
    }

    Ok(())
}
