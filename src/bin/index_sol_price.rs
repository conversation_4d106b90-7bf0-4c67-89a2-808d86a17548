use std::env;
use anyhow::Context;
use print_meme_collector_sol::{
    config::APP_CONFIG, constants::NATIVE_SOL_ADDRESS, db::common::setup_mongodb,
    service::redis_service::RedisService, shared::TOKEN_PRICES_REPOSITORY,
    utils::init_standard_tracing,
};
use std::time::Duration;
use reqwest::{Client, Url};
use serde_json::Value;
use tokio::time::sleep;

async fn get_solana_price_usd() -> Result<f64, anyhow::Error> {
    tracing::info!("Fetching solana price in usd");

    let api_key = env::var("JUPITER_API_KEY")
        .context("Missing JUPITER_API_KEY env var")?;

    let url = Url::parse_with_params(
        "https://api.jup.ag/price/v3",
        &[("ids", NATIVE_SOL_ADDRESS)],
    )?;
    let client = Client::builder()
        .timeout(Duration::from_secs(10))
        .build()
        .context("Failed to build HTTP client")?;

    let result = client
        .get(url)
        .header("x-api-key", api_key)
        .header("Accept", "application/json")
        .send()
        .await;

    println!("response: {:?}", result);

    match result  {
        Ok(response) => {
            let status = response.status();
            if !status.is_success() {
                let text = response.text().await.unwrap_or_default();
                return Err(anyhow::anyhow!("API returned {}: {}", status, text));
            }

            let body: Value = response
                .json()
                .await
                .context("Failed to parse JSON response")?;

            let price = body[NATIVE_SOL_ADDRESS]["usdPrice"]
                .as_f64()
                .context("Missing or invalid usdPrice in response")?;

            Ok(price)
        }
        Err(err) => {
            if err.is_timeout() {
                Err(anyhow::anyhow!("Request to Jupiter API timed out"))
            } else {
                Err(err).context("Failed to send request to Jupiter API")
            }
        }
    }
}

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    dotenv::dotenv().ok();
    init_standard_tracing(env!("CARGO_CRATE_NAME"));
    setup_mongodb(&APP_CONFIG.mongodb_uri).await;
    let redis_service: &'static RedisService = RedisService::new().await;
    let sleep_duration = 30;

    loop {
        match get_solana_price_usd().await {
            Ok(price) => {
                redis_service
                    .set_sol_price_usd(price.to_string(), sleep_duration)
                    .await
                    .context("Failed to set SOL price in Redis")?;
                TOKEN_PRICES_REPOSITORY
                    .upsert_token_price(NATIVE_SOL_ADDRESS, price)
                    .await
                    .context("Failed to upsert SOL price in MongoDB")?;
            }
            Err(e) => {
                tracing::error!("{}", e);
            }
        };

        sleep(Duration::from_millis(1000 * (sleep_duration - 5))).await
    }
}
