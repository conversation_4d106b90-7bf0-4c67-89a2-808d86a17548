use std::str::FromStr;

use anchor_lang::prelude::{
    borsh::{<PERSON>rshDeserialize, BorshSerialize},
    AccountMeta,
};
use anyhow::Error;
use rand::rngs::OsRng;
use raydium_amm_v3::states::POOL_SEED;
use solana_client::rpc_config::RpcSendTransactionConfig;
use solana_sdk::{
    commitment_config::CommitmentConfig, instruction::Instruction, program_pack::Pack,
    pubkey::Pubkey, signature::Keypair, signer::Signer, system_program, transaction::Transaction,
};

use bson::{DateTime, Decimal128};

use spl_token::state::Account;

use crate::{
    config::APP_CONFIG,
    constants::NetworkConfig,
    db::bonding_curve_error::{BondingCurveErrors, CompleteEventError},
    service::coin_handler::CompleteEvent,
    shared::{BONDING_CURVE_ERRORS_REPOSITORY, COINS_REPOSITORY, SOLANA_CLIENT, WSOL_PUBKEY},
    utils::{
        clmm_instructions,
        clmm_math::sqrt_price_x64_to_price,
        clmm_utils::{self, calculate_liquidity_change},
        close_wsol_account, create_wsol_account, deserialize_anchor_account, get_anchor_account,
        load_solana_keypair_from_private_key,
    },
};

pub async fn handle_complete_bonding_curve(
    token_mint: &Pubkey,
    sol_amount: u64,
    complete_event: &CompleteEvent,
) -> Result<(), Error> {
    //get token amount

    let bonding_curve = derive_bonding_curve_data(token_mint).await?;
    let initial_real_token_reserve = bonding_curve
        .token_total_supply
        .checked_mul(7000)
        .unwrap()
        .checked_div(10000)
        .unwrap();
    let input_pc_amount = bonding_curve
        .token_total_supply
        .checked_sub(initial_real_token_reserve)
        .unwrap();
    let input_coin_amount = sol_amount
        .checked_mul(93)
        .unwrap()
        .checked_div(100)
        .unwrap();

    // transfer adding token from airdrop account to admin
    let price = (input_pc_amount as f64 / 10u64.pow(6) as f64)
        / (input_coin_amount as f64 / 10u64.pow(9) as f64);

    let result = withdraw_from_bonding_curve(token_mint).await;

    if let Err(e) = result {
        tracing::error!(
            "STEP1:withdraw_from_bonding_curve ERROR: {} token_mint {}",
            e,
            token_mint
        );

        // Construct CompleteEventError (assuming it's defined somewhere)
        let complete_event_error = CompleteEventError {
            user: complete_event.user.to_string(),
            mint: token_mint.to_string(),
            bonding_curve: bonding_curve.mint.to_string(),
            timestamp: complete_event.timestamp,
            sol_amount,
            token_amount: input_pc_amount,
        };

        let bonding_curve_error = BondingCurveErrors {
            step: "1".to_string(),
            error: e.to_string(),
            event_data: Some(complete_event_error),
            token_address: token_mint.to_string(), // assuming token_address is u32
            input_pc_amount: Decimal128::from_str(&input_pc_amount.to_string()).unwrap(),
            input_sol_amount: Decimal128::from_str(&input_coin_amount.to_string()).unwrap(),
            initial_price: Decimal128::from_str(&price.to_string()).unwrap(),
            listing_pair_id: None,
            create_pool_signature: None,
            created_at: DateTime::now(),
            updated_at: DateTime::now(),
            is_fixed: false,
        };

        // Save error to DB
        if let Err(db_err) = BONDING_CURVE_ERRORS_REPOSITORY
            .create(bonding_curve_error)
            .await
        {
            tracing::error!("Failed to insert BondingCurveError: {}", db_err);
        }

        return Err(e); // propagate original error
    }

    let pool_account_key = create_clmm_pool(
        *WSOL_PUBKEY,
        *token_mint,
        price,
        input_coin_amount,
        input_pc_amount,
    )
    .await?;

    COINS_REPOSITORY
        .complete_bonding_curve(
            &token_mint.to_string(),
            &pool_account_key.to_string(),
            price,
        )
        .await?;

    Ok(())
}

pub async fn derive_bonding_curve_data(token_mint: &Pubkey) -> Result<BondingCurve, Error> {
    let program_id = Pubkey::from_str(&APP_CONFIG.program_id)?;
    let (bonding_curve, _) =
        Pubkey::find_program_address(&[b"bonding-curve", token_mint.as_ref()], &program_id);

    let bonding_curve_account = SOLANA_CLIENT.get_account(&bonding_curve).await?;

    println!(
        "bonding_curve_account : {:?} data:length {}",
        bonding_curve_account,
        &bonding_curve_account.data.len()
    );
    let data = &bonding_curve_account.data[8..]; // Skip Anchor discriminator

    let bonding_curve_data = BondingCurve::try_from_slice(data)?;
    println!("bonding_curve_data : {:?}", bonding_curve_data);

    Ok(bonding_curve_data)
}

async fn withdraw_from_bonding_curve(mint: &Pubkey) -> Result<(), Error> {
    let wallet = load_solana_keypair_from_private_key(&APP_CONFIG.private_key);
    let program_id = Pubkey::from_str(&APP_CONFIG.program_id)?;

    let (global, _) = Pubkey::find_program_address(&[b"global"], &program_id);
    let (bonding_curve, _) =
        Pubkey::find_program_address(&[b"bonding-curve", mint.as_ref()], &program_id);
    let (last_withdraw, _) = Pubkey::find_program_address(&[b"last-withdraw"], &program_id);

    let associated_bonding_curve =
        spl_associated_token_account::get_associated_token_address(&bonding_curve, mint);
    let associated_user =
        spl_associated_token_account::get_associated_token_address(&wallet.pubkey(), mint);

    let mut instructions = vec![];

    if SOLANA_CLIENT.get_account(&associated_user).await.is_err() {
        let create_ata_ix =
            spl_associated_token_account::instruction::create_associated_token_account(
                &wallet.pubkey(), // Payer
                &wallet.pubkey(), // Wallet
                mint,             // Mint
                &spl_token::id(), // Token Program
            );
        instructions.push(create_ata_ix);
    }

    let ix_data = vec![183, 18, 70, 156, 148, 109, 161, 34]; // withdraw instruction discriminator

    let withdraw_instruction = Instruction {
        program_id,
        accounts: vec![
            AccountMeta::new(wallet.pubkey(), true),  // user (signer)
            AccountMeta::new_readonly(global, false), // global
            AccountMeta::new(*mint, false),           // mint
            AccountMeta::new(last_withdraw, false),   // last_withdraw
            AccountMeta::new(bonding_curve, false),   // bonding_curve
            AccountMeta::new(associated_bonding_curve, false), // associated_bonding_curve
            AccountMeta::new(associated_user, false), // associated_user
            AccountMeta::new_readonly(system_program::id(), false), // system_program
            AccountMeta::new_readonly(spl_token::id(), false), // token_program
        ],
        data: ix_data,
    };

    instructions.push(withdraw_instruction);

    let transaction = Transaction::new_signed_with_payer(
        &instructions,
        Some(&wallet.pubkey()),
        &[&wallet],
        SOLANA_CLIENT.get_latest_blockhash().await?,
    );

    let signature = SOLANA_CLIENT
        .send_and_confirm_transaction_with_spinner_and_config(
            &transaction,
            CommitmentConfig::confirmed(),
            RpcSendTransactionConfig {
                skip_preflight: true,
                ..RpcSendTransactionConfig::default()
            },
        )
        .await?;

    tracing::info!(
        "Withdraw token {} transaction signature: {}",
        mint,
        signature
    );

    Ok(())
}

pub async fn create_clmm_pool(
    mint0: Pubkey,
    mint1: Pubkey,
    price: f64,
    sol_amount: u64,
    input_pc_amount: u64,
) -> Result<String, Error> {
    let wallet = load_solana_keypair_from_private_key(&APP_CONFIG.private_key);
    let raydium_clmm_program =
        Pubkey::from_str(&NetworkConfig::from_network(&APP_CONFIG.network).clmm_program)?;
    let result = clmm_utils::create_pool_price(&SOLANA_CLIENT, mint0, mint1, price)
        .await
        .map_err(|e| {
            tracing::error!("STEP2:create_pool_price ERROR: {}", e);
            e
        })?;

    let (amm_config_key, __bump) = Pubkey::find_program_address(
        &[
            raydium_amm_v3::states::AMM_CONFIG_SEED.as_bytes(),
            &(1_u16).to_be_bytes(),
        ],
        &raydium_clmm_program,
    );

    let (pool_account_key, __bump) = Pubkey::find_program_address(
        &[
            POOL_SEED.as_bytes(),
            amm_config_key.to_bytes().as_ref(),
            mint0.to_bytes().as_ref(),
            mint1.to_bytes().as_ref(),
        ],
        &raydium_clmm_program,
    );

    let create_pool_instr = clmm_instructions::create_pool_instr(
        amm_config_key,
        result.mint0,
        result.mint1,
        result.mint0_token_program,
        result.mint1_token_program,
        result.sqrt_price_x64,
        0,
    )?;

    // Create pool
    let transaction: Transaction = Transaction::new_signed_with_payer(
        &create_pool_instr,
        Some(&wallet.pubkey()),
        &[&wallet],
        SOLANA_CLIENT.get_latest_blockhash().await.map_err(|e| {
            tracing::error!(
                "STEP2:get_latest_blockhash ERROR: {} pool key {}",
                e,
                pool_account_key
            );
            e
        })?,
    );

    let signature = match SOLANA_CLIENT
        .send_and_confirm_transaction_with_spinner_and_config(
            &transaction,
            CommitmentConfig::confirmed(),
            RpcSendTransactionConfig {
                skip_preflight: true,
                ..RpcSendTransactionConfig::default()
            },
        )
        .await
    {
        Ok(sig) => sig,
        Err(e) => {
            tracing::error!(
                "STEP2: signature error: {} pool_account_key: {}",
                e,
                pool_account_key
            );

            let bonding_curve_error = BondingCurveErrors {
                step: "2".to_string(),
                error: e.to_string(),
                event_data: None,
                token_address: mint1.to_string(),
                input_pc_amount: Decimal128::from_str(&input_pc_amount.to_string()).unwrap(),
                input_sol_amount: Decimal128::from_str(&sol_amount.to_string()).unwrap(),
                initial_price: Decimal128::from_str(&price.to_string()).unwrap(),
                listing_pair_id: Some(pool_account_key.to_string()),
                create_pool_signature: None,
                created_at: DateTime::now(),
                updated_at: DateTime::now(),
                is_fixed: false,
            };

            if let Err(db_err) = BONDING_CURVE_ERRORS_REPOSITORY
                .create(bonding_curve_error)
                .await
            {
                tracing::error!("STEP2: failed to insert BondingCurveError: {}", db_err);
            }

            return Err(e.into());
        }
    };

    tracing::info!(
        "Create CLMM pool: {}, transaction signature: {}",
        pool_account_key,
        signature
    );

    let mint_rent_exempt = SOLANA_CLIENT
        .get_minimum_balance_for_rent_exemption(Account::LEN)
        .await
        .map_err(|e| {
            tracing::error!(
                "STEP3:mint_rent_exempt ERROR: {} pool key {}",
                e,
                pool_account_key
            );
            e
        })?;

    let mut open_position_instructions: Vec<Instruction> = vec![];

    let (wsol_pubkey, wsol_instructions): (Pubkey, Vec<Instruction>) =
        create_wsol_account(&wallet, mint_rent_exempt + sol_amount).map_err(|e| {
            tracing::error!(
                "STEP3:create_wsol_account ERROR: {} pool key {}",
                e,
                pool_account_key
            );
            e
        })?;

    open_position_instructions.extend_from_slice(&wsol_instructions);

    let pool =
        get_anchor_account::<raydium_amm_v3::states::PoolState>(&SOLANA_CLIENT, &pool_account_key)
            .await
            .map_err(|e| {
                tracing::error!(
                    "STEP3:handle_open_position ERROR: {} pool key {}",
                    e,
                    pool_account_key
                );
                e
            })?
            .unwrap();

    let (open_position_instr, position_keypair) = handle_open_position(
        pool_account_key,
        Some(wsol_pubkey),
        None,
        sqrt_price_x64_to_price(**********, pool.mint_decimals_0, pool.mint_decimals_1),
        sqrt_price_x64_to_price(
            79226673521066979257578248091,
            pool.mint_decimals_0,
            pool.mint_decimals_1,
        ),
        ((sol_amount as f64) * price
            / 10.0_f64.powi(pool.mint_decimals_0 as i32 - pool.mint_decimals_1 as i32))
            as u64,
        true,
        false,
    )
    .await
    .map_err(|e| {
        tracing::error!(
            "STEP3:handle_open_position ERROR: {} pool key {}",
            e,
            pool_account_key
        );
        e
    })?;

    open_position_instructions.extend_from_slice(&open_position_instr);
    open_position_instructions.extend_from_slice(&[close_wsol_account(&wallet, &wsol_pubkey)?]);

    let transaction = Transaction::new_signed_with_payer(
        &open_position_instructions,
        Some(&wallet.pubkey()),
        &[&wallet, &position_keypair],
        SOLANA_CLIENT.get_latest_blockhash().await.map_err(|e| {
            tracing::error!(
                "STEP3:get_latest_blockhash ERROR: {} pool key {} instructions {:?}",
                e,
                pool_account_key,
                open_position_instr
            );
            e
        })?,
    );

    let signature = match SOLANA_CLIENT
        .send_and_confirm_transaction_with_spinner_and_config(
            &transaction,
            CommitmentConfig::confirmed(),
            RpcSendTransactionConfig {
                skip_preflight: true,
                ..RpcSendTransactionConfig::default()
            },
        )
        .await
    {
        Ok(sig) => sig,
        Err(e) => {
            tracing::error!(
                "STEP3:signature ERROR: {} pool key {} instructions {:?}",
                e,
                pool_account_key,
                open_position_instr
            );

            let bonding_curve_error = BondingCurveErrors {
                step: "3".to_string(),
                error: e.to_string(),
                event_data: None,
                token_address: mint1.to_string(),
                input_pc_amount: Decimal128::from_str(&input_pc_amount.to_string()).unwrap(),
                input_sol_amount: Decimal128::from_str(&sol_amount.to_string()).unwrap(),
                initial_price: Decimal128::from_str(&price.to_string()).unwrap(),
                listing_pair_id: Some(pool_account_key.to_string()),
                create_pool_signature: None,
                created_at: DateTime::now(),
                updated_at: DateTime::now(),
                is_fixed: false,
            };

            if let Err(db_err) = BONDING_CURVE_ERRORS_REPOSITORY
                .create(bonding_curve_error)
                .await
            {
                tracing::error!("STEP3: failed to insert BondingCurveError: {}", db_err);
            }

            return Err(e.into());
        }
    };

    tracing::info!(
        "Open position: {}, transaction signature: {}, position: {}",
        pool_account_key,
        signature,
        position_keypair.pubkey()
    );

    Ok(pool_account_key.to_string())
}

#[allow(clippy::too_many_arguments)]
pub async fn handle_open_position(
    pool_id: Pubkey,
    deposit_token0: Option<Pubkey>,
    deposit_token1: Option<Pubkey>,
    tick_lower_price: f64,
    tick_upper_price: f64,
    amount_specified: u64,
    base_token1: bool,
    without_metadata: bool,
) -> Result<(Vec<Instruction>, Keypair), Error> {
    let base_token0 = !base_token1;
    let with_metadata = !without_metadata;

    let raydium_clmm_program =
        Pubkey::from_str(&NetworkConfig::from_network(&APP_CONFIG.network).clmm_program)?;
    let wallet = load_solana_keypair_from_private_key(&APP_CONFIG.private_key);

    let result = calculate_liquidity_change(
        &SOLANA_CLIENT,
        pool_id,
        tick_lower_price,
        tick_upper_price,
        amount_specified,
        100,
        false,
        base_token0,
    )
    .await?;

    let deposit_token0 = deposit_token0.unwrap_or_else(|| {
        spl_associated_token_account::get_associated_token_address_with_program_id(
            &wallet.pubkey(),
            &result.mint0,
            &result.mint0_token_program,
        )
    });
    let deposit_token1 = deposit_token1.unwrap_or_else(|| {
        spl_associated_token_account::get_associated_token_address_with_program_id(
            &wallet.pubkey(),
            &result.mint1,
            &result.mint1_token_program,
        )
    });

    let (_nft_tokens, positions) = clmm_utils::get_nft_accounts_and_positions_by_owner(
        &SOLANA_CLIENT,
        &wallet.pubkey(),
        &raydium_clmm_program,
    )
    .await;
    let rsps = SOLANA_CLIENT.get_multiple_accounts(&positions).await?;

    let mut user_positions = Vec::new();
    for rsp in rsps {
        match rsp {
            None => continue,
            Some(rsp) => {
                let position = deserialize_anchor_account::<
                    raydium_amm_v3::states::PersonalPositionState,
                >(&rsp)?;
                user_positions.push(position);
            }
        }
    }
    let find_position = user_positions
        .iter()
        .find(|position| {
            position.pool_id == pool_id
                && position.tick_lower_index == result.tick_lower_index
                && position.tick_upper_index == result.tick_upper_index
        })
        .cloned()
        .unwrap_or_default();

    if find_position.nft_mint == Pubkey::default() {
        let tickarray_bitmap_extension = Pubkey::find_program_address(
            &[
                raydium_amm_v3::states::POOL_TICK_ARRAY_BITMAP_SEED.as_bytes(),
                pool_id.to_bytes().as_ref(),
            ],
            &raydium_clmm_program,
        )
        .0;

        let nft_mint = Keypair::generate(&mut OsRng);
        let nft_mint_key = nft_mint.pubkey();

        let remaining_accounts = vec![AccountMeta::new(tickarray_bitmap_extension, false)];

        let open_position_instr = clmm_instructions::open_position_instr(
            pool_id,
            result.vault0,
            result.vault1,
            result.mint0,
            result.mint1,
            nft_mint_key,
            wallet.pubkey(),
            deposit_token0,
            deposit_token1,
            remaining_accounts,
            result.liquidity,
            result.amount_0,
            result.amount_1,
            result.tick_lower_index,
            result.tick_upper_index,
            result.tick_array_lower_start_index,
            result.tick_array_upper_start_index,
            with_metadata,
        )?;

        Ok((open_position_instr, nft_mint))
    } else {
        panic!("personal position exist:{:?}", find_position);
    }
}

#[derive(BorshDeserialize, BorshSerialize, Debug)]
pub struct BondingCurve {
    /// virtual_token_reserves
    pub virtual_token_reserves: u64,

    /// virtual_sol_reserves
    pub virtual_sol_reserves: u64,

    /// real_token_reserves
    pub real_token_reserves: u64,

    /// real_sol_reserves
    pub real_sol_reserves: u64,

    /// token_total_supply
    pub token_total_supply: u64,

    /// complete
    pub complete: bool,

    /// mint
    pub mint: Pubkey,

    /// creator
    pub creator: Pubkey,
}
