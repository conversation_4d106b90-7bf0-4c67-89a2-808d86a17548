use anyhow::Ok;
use reqwest::Client;
use serde_json::json;
use solana_sdk::pubkey::Pubkey;

use crate::{config::APP_CONFIG, constants::{NATIVE_SOL_ADDRESS, SOLANA_DECIMALS}, service::{coin_handler::WithdrawCreatorFeeEvent, trade_handler::TradeEvent}, shared::TOKEN_PRICES_REPOSITORY};

pub async fn handle_trigger_airdrop(token_mint: &Pubkey) -> anyhow::Result<(), anyhow::Error> {
    // using http to trigger airdrop event in node js service!!!
    let bearer_token = get_admin_token().await?;
    trigger_airdrop(bearer_token, token_mint.to_string()).await?;
    Ok(())
}



pub async fn handle_add_point_for_user(event: &TradeEvent) -> Result<(), anyhow::Error> {
    let bearer_token: String = get_admin_token().await?;
    add_points_to_user(bearer_token, event).await?;
    Ok(())
}

pub async fn handle_withdraw_creator_fee(event: &WithdrawCreatorFeeEvent, signature: String)-> Result<(), anyhow::Error> {
    let bearer_token: String = get_admin_token().await?;
    let client = Client::new();
    let env = Environment::from_env();
    let config = env.get_config();
    let quote_price_usd = TOKEN_PRICES_REPOSITORY
    .get_cached_token_price(NATIVE_SOL_ADDRESS)
    .await
    .map_err(|e| {
        tracing::error!("Failed to get SOL price: {}", e);
        e
    })?;
    let sol_amount_without_decimal = event.sol_amount as f64 / 10f64.powi(SOLANA_DECIMALS as i32);
    let usd_amount = sol_amount_without_decimal * quote_price_usd;

    tracing::info!("Successfully added points to user {}" , usd_amount);

    tracing::info!("Calling add_point_url: {}", config.add_point_url);
    tracing::info!("User: {}", event.creator.to_string());
    tracing::info!("USD Amount: {}", usd_amount);
    tracing::info!("Full URL: {}?user-address={}&usd-amount={}&mint={}&sig={}",config.creator_fee_url, event.creator.to_string(), usd_amount, event.mint.to_string(),signature);

    let response = client
        .post(&format!(
            "{}?user-address={}&usd-amount={}&sol-amount={}&mint={}&sig={}",
            config.creator_fee_url, event.creator.to_string(), usd_amount,sol_amount_without_decimal, event.mint.to_string(),signature
        ))
        .header("accept", "*/*")
        .bearer_auth(bearer_token)
        .send()
        .await?;
    
    let status = response.status();
    let body = response.text().await?;
    println!("status: {}", status);
    println!("body: {}", body);
    Ok(())

}




async fn add_points_to_user( bearer_token: String,event: &TradeEvent) -> Result<(), anyhow::Error> {
    let client = Client::new();
    let env = Environment::from_env();
    let config = env.get_config();

    let quote_price_usd = TOKEN_PRICES_REPOSITORY
    .get_cached_token_price(NATIVE_SOL_ADDRESS)
    .await
    .map_err(|e| {
        tracing::error!("Failed to get SOL price: {}", e);
        e
    })?;

    let sol_amount_without_decimal = event.sol_amount as f64 / 10f64.powi(SOLANA_DECIMALS as i32);
    let usd_amount = sol_amount_without_decimal * quote_price_usd;
    let _response = client
        .post(&format!(
            "{}?user-address={}&usd-amount={}",
            config.add_point_url, event.user.to_string(), usd_amount
        ))
        .header("accept", "*/*")
        .bearer_auth(bearer_token)
        .send()
        .await?;
    Ok(())

}

pub async fn get_admin_token() -> Result<String, anyhow::Error> {
    let client = Client::new();

    let env = Environment::from_env();
    let config = env.get_config();
    let admin_username = &APP_CONFIG.admin_username;
    let admin_pw = &APP_CONFIG.admin_pw;

    let body = json!({
        "username": admin_username,
        "password": admin_pw
    });
    let response = client
        .post(config.auth_admin_url)
        .header("accept", "*/*")
        .header("Content-Type", "application/json")
        .json(&body)
        .send()
        .await?;

    let text = response.text().await?;
    let data = serde_json::from_str::<serde_json::Value>(&text)?;
    let access_tokens = data["data"]["access_tokens"].as_str().unwrap();

    Ok(access_tokens.to_string())
}

async fn trigger_airdrop(
    bearer_token: String,
    token_mint_address: String,
) -> Result<(), anyhow::Error> {
    let client = Client::new();
    let env = Environment::from_env();
    let config = env.get_config();

    let response = client
        .post(&format!(
            "{}?token_address={}",
            config.airdrop_url, token_mint_address
        ))
        .header("accept", "*/*")
        .bearer_auth(bearer_token)
        .send()
        .await?;

    let status = response.status();
    let body = response.text().await?;
    println!("status: {}", status);
    println!("body: {}", body);

    Ok(())
}

#[derive(Debug)]
enum Environment {
    Development,
    Staging,
    Production,
}

#[derive(Debug)]
struct Config {
    auth_admin_url: String,
    airdrop_url: String,
    add_point_url:String,
    creator_fee_url:String,
}

impl Environment {
    fn from_env() -> Self {
        match APP_CONFIG.rust_env.as_str() {
            "production" => Environment::Production,
            "staging" => Environment::Staging,
            _ => Environment::Development,
        }
    }

    fn get_config(&self) -> Config {
        match self {
            Environment::Development => Config {
                auth_admin_url: "https://api-smartpocke.sotatek.works/api/v1/auth/admin".to_string(),
                airdrop_url: "https://api-smartpocke.sotatek.works/api/v1/admin/air-drop/snapshot-ranking".to_string(),
                add_point_url: "https://api-smartpocke.sotatek.works/api/v1/admin/air-drop/add-point".to_string(),
                creator_fee_url:"https://api-smartpocke.sotatek.works/api/v1/admin/air-drop/creator".to_string()
                
            },
            Environment::Staging => Config {
                auth_admin_url: "https://api.stg-smartpocke.sotatek.works/api/v1/auth/admin".to_string(),
                airdrop_url: "https://api.stg-smartpocke.sotatek.works/api/v1/admin/air-drop/snapshot-ranking".to_string(),
                add_point_url: "https://api.stg-smartpocke.sotatek.works/api/v1/admin/air-drop/add-point".to_string(),
                creator_fee_url: "https://api.stg-smartpocke.sotatek.works/api/v1/admin/air-drop/creator".to_string(),


            },
            Environment::Production => Config {
                auth_admin_url: "https://pointapi.smapocke-marketplace.app/api/v1/auth/admin".to_string(),
                airdrop_url: "https://pointapi.smapocke-marketplace.app/api/v1/admin/air-drop/snapshot-ranking".to_string(),
                add_point_url: "https://pointapi.smapocke-marketplace.app/api/v1/admin/air-drop/add-point".to_string(),
                creator_fee_url: "https://pointapi.smapocke-marketplace.app/api/v1/admin/air-drop/creator".to_string(),
            },
        }
    }
}
