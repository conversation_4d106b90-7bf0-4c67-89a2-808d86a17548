use std::str::FromStr;

use anchor_lang::prelude::*;
use bson::Decimal128;
use solana_sdk::pubkey::Pubkey;

use crate::{
    constants::{NATIVE_SOL_ADDRESS, SOLANA_DECIMALS},
    db::{
        shared_traits::RepositorySharedMethod,
        trades::{ETradeType, Trades},
    },
    shared::{COINS_REPOSITORY, TOKEN_PRICES_REPOSITORY, TRADES_REPOSITORY},
    types::PubTradesData,
    utils::get_token_decimals,
};

use super::{common::handle_update_candle_per_swap, redis_service::RedisEmitter};

pub async fn handle_trade_event(
    signature: String,
    slot: u64,
    index: usize,
    event: &TradeEvent,
    redis_emitter: &RedisEmitter,
) -> anyhow::Result<()> {
    let token_decimals = get_token_decimals(&event.mint).await?;
    let maker = event.user.to_string();
    let token_address = event.mint.to_string();

    let virtual_sol_reserves_without_decimals =
        event.virtual_sol_reserves as f64 / 10f64.powi(SOLANA_DECIMALS as i32);
    let virtual_token_reserves_without_decimals =
        event.virtual_token_reserves as f64 / 10f64.powi(token_decimals as i32);
    let base_price_quote =
        virtual_sol_reserves_without_decimals / virtual_token_reserves_without_decimals;

    let quote_price_usd = TOKEN_PRICES_REPOSITORY
        .get_cached_token_price(NATIVE_SOL_ADDRESS)
        .await
        .map_err(|e| {
            tracing::error!("Failed to get SOL price: {}", e);
            e
        })?;

    let token_info = COINS_REPOSITORY
        .get_token_by_address(&token_address)
        .await?;
    let token_symbol = if let Some(token) = token_info {
        Some(token.symbol)
    } else {
        None
    };

    let base_price_usd = quote_price_usd * base_price_quote;

    let sol_amount_without_decimals = event.sol_amount as f64 / 10f64.powi(SOLANA_DECIMALS as i32);
    let token_amount_without_decimals =
        event.token_amount as f64 / 10f64.powi(token_decimals as i32);

    let volume_usd = sol_amount_without_decimals * quote_price_usd;

    let to_decimal_u64 = |val: u64| Decimal128::from_str(&val.to_string()).unwrap();

    let creator_fee_without_decimal = event.creator_fee as f64 / 10f64.powi(SOLANA_DECIMALS as i32);
    let creator_usd_fee = creator_fee_without_decimal * quote_price_usd;
    let airdrop_fee_without_decial = event.airdrop_fee as f64 / 10f64.powi(SOLANA_DECIMALS as i32);
    let airdrop_usd_fee = airdrop_fee_without_decial * quote_price_usd;

    let trade_record = Trades {
        token_address: token_address.clone(),
        token_symbol: token_symbol.clone(),
        quote_amount: sol_amount_without_decimals.to_string(),
        base_amount: token_amount_without_decimals.to_string(),
        trade_type: if event.is_buy {
            ETradeType::Buy
        } else {
            ETradeType::Sell
        },
        maker: maker.clone(),
        timestamp: event.timestamp as u64,
        virtual_sol_reserves: to_decimal_u64(event.virtual_sol_reserves),
        virtual_token_reserves: to_decimal_u64(event.virtual_token_reserves),
        real_sol_reserves: to_decimal_u64(event.real_sol_reserves),
        real_token_reserves: to_decimal_u64(event.real_token_reserves),
        slot,
        hash: signature.clone(),
        index: index as u64,
        price: base_price_quote.to_string(),
        price_usd: base_price_usd.to_string(),
        volume: sol_amount_without_decimals.to_string(),
        volume_usd: volume_usd.to_string(),
        creator_sol_fee: creator_fee_without_decimal.to_string(),
        creator_usd_fee: creator_usd_fee.to_string(),
        airdrop_sol_fee: airdrop_fee_without_decial.to_string(),
        airdrop_usd_fee: airdrop_usd_fee.to_string(),
    };

    let pub_trade_data = PubTradesData {
        slot,
        hash: signature,
        token_address: token_address.clone(),
        index: index as u64,
        maker: maker.clone(),
        trade_type: trade_record.trade_type,
        base_amount: trade_record.base_amount.to_string(),
        quote_amount: trade_record.quote_amount.to_string(),
        price: trade_record.price.to_string(),
        price_usd: trade_record.price_usd.to_string(),
        volume: trade_record.volume.to_string(),
        volume_usd: trade_record.volume_usd.to_string(),
        timestamp: event.timestamp as u64,
        virtual_sol_reserves: trade_record.virtual_sol_reserves.to_string(),
        virtual_token_reserves: trade_record.virtual_token_reserves.to_string(),
        real_sol_reserves: trade_record.real_sol_reserves.to_string(),
        real_token_reserves: trade_record.real_token_reserves.to_string(),
        token_symbol,
    };

    redis_emitter.emit_room(
        &format!("SUBSCRIBE_COIN::{}", event.mint),
        "CreatedTrade",
        &serde_json::to_string(&pub_trade_data)?,
    );

    TRADES_REPOSITORY
        .insert_one_or_ignore(trade_record, None)
        .await?;

    handle_update_candle_per_swap(
        token_address,
        NATIVE_SOL_ADDRESS.to_string(),
        event.timestamp as u64,
        base_price_quote,
        base_price_usd,
        token_amount_without_decimals,
        sol_amount_without_decimals,
    )
    .await?;

    Ok(())
}

/// TradeEvent
#[event]
#[derive(Debug)]
pub struct TradeEvent {
    pub mint: Pubkey,
    pub sol_amount: u64,
    pub token_amount: u64,
    pub is_buy: bool,
    pub user: Pubkey,
    pub timestamp: i64,
    pub virtual_sol_reserves: u64,
    pub virtual_token_reserves: u64,
    pub real_sol_reserves: u64,
    pub real_token_reserves: u64,
    pub creator_fee: u64,
    pub airdrop_fee: u64,
}
