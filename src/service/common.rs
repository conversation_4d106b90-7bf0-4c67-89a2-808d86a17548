use crate::db::candles::{Candle, Candles};
use crate::db::shared_traits::RepositorySharedMethod;
use crate::shared::{CANDLES_REPOSITORY, PARAMS_REPOSITORY};
use crate::utils::un_decimals_from_str;

use bson::{doc, <PERSON><PERSON>, Decimal128};
use mongodb::options::FindOneOptions;
use std::str::FromStr;

pub const TOKEN_DECIMALS: u32 = 6;
pub const SOL_DECIMALS: u32 = 9;
const TOKEN_TOTAL_SUPPLY: u32 = 1000000;

#[allow(clippy::too_many_arguments)]
async fn update_candle_per_swap(
    base_token: String,
    _quote_token: String,
    timestamp: u64,
    price: f64,
    price_usd: f64,
    base_amount_without_decimals: f64,
    quote_amount_without_decimals: f64,
) -> anyhow::Result<()> {
    let resolution_options = [1, 60, 900, 3600];

    let volume_base_f64 = base_amount_without_decimals;
    let volume_quote_f64 = quote_amount_without_decimals;
    let volume_usd_f64 = volume_base_f64 * price_usd;

    let bonding_curve_params = PARAMS_REPOSITORY.find_one(doc! {}, None).await?;
    let Some(bonding_curve_params) = bonding_curve_params else {
        return Err(anyhow::anyhow!("Failed to find bonding curve params: {:?}", bonding_curve_params));
    };

    let volume_base = Decimal128::from_str(&volume_base_f64.to_string()).unwrap();
    let volume_quote = Decimal128::from_str(&volume_quote_f64.to_string()).unwrap();
    let volume_usd = Decimal128::from_str(&volume_usd_f64.to_string()).unwrap();

    // TODO: get token total supply from db
    let token_base_supply = f64::from_str(&TOKEN_TOTAL_SUPPLY.to_string()).unwrap();

    let market_cap = token_base_supply * price;
    let market_cap_usd = token_base_supply * price_usd;

    for resolution in resolution_options {
        let resolution_timestamp = timestamp - (timestamp % resolution);

        let options = FindOneOptions::builder()
            .sort(doc! { "timestamp": -1_i32 })
            .build();

        let pre_candle = CANDLES_REPOSITORY
            .find_one(
                doc! {"tokenAddress": base_token.clone(), "resolution": resolution as u32},
                options,
            )
            .await?;

        let mut price_low = price;
        let mut price_high = price;
        let mut price_open = price;

        let mut price_usd_low = price_usd;
        let mut price_usd_high = price_usd;
        let mut price_usd_open = price_usd;

        let mut market_cap_low = market_cap;
        let mut market_cap_high = market_cap;
        let mut market_cap_open = market_cap;

        let mut market_cap_usd_low = market_cap_usd;
        let mut market_cap_usd_high = market_cap_usd;
        let mut market_cap_usd_open = market_cap_usd;

        // Case 1
        // when candle exist
        if let Some(candle) = pre_candle
            .clone()
            .filter(|c| c.timestamp == resolution_timestamp)
        {
            let update_doc = doc! {
                "$set": {
                    "price": {
                        "low": price.min(candle.price.low.parse::<f64>().unwrap_or(price)).to_string(),
                        "high": price.max(candle.price.high.parse::<f64>().unwrap_or(price)).to_string(),
                        "open": candle.price.open,
                        "close": price.to_string(),
                    },
                    "priceUsd": {
                        "low": price_usd.min(candle.price_usd.low.parse::<f64>().unwrap_or(price_usd)).to_string(),
                        "high": price_usd.max(candle.price_usd.high.parse::<f64>().unwrap_or(price_usd)).to_string(),
                        "open": candle.price_usd.open,
                        "close": price_usd.to_string(),
                    },
                    "marketCap": {
                        "low": market_cap.min(candle.market_cap.low.parse::<f64>().unwrap_or(market_cap)).to_string(),
                        "high": market_cap.max(candle.market_cap.high.parse::<f64>().unwrap_or(market_cap)).to_string(),
                        "open": candle.market_cap.open,
                        "close": market_cap.to_string(),
                    },
                    "marketCapUsd": {
                        "low": market_cap_usd.min(candle.market_cap_usd.low.parse::<f64>().unwrap_or(market_cap_usd)).to_string(),
                        "high": market_cap_usd.max(candle.market_cap_usd.high.parse::<f64>().unwrap_or(market_cap_usd)).to_string(),
                        "open": candle.market_cap_usd.open,
                        "close": market_cap_usd.to_string(),
                    },
                    "volumeQuote": (candle.volume_quote.parse::<f64>().unwrap_or(0.0) + volume_quote_f64).to_string(),
                    "volumeBase": (candle.volume_base.parse::<f64>().unwrap_or(0.0) + volume_base_f64).to_string(),
                    "volumeUsd": (candle.volume_usd.parse::<f64>().unwrap_or(0.0) + volume_usd_f64).to_string(),
                }
            };

            CANDLES_REPOSITORY
                .update_one(doc! {"tokenAddress": base_token.clone(), "resolution": resolution as u32, "timestamp": Bson::Int64(resolution_timestamp as i64)}, update_doc, None)
                .await
                .map_err(|err| tracing::error!("Error updating candle: {:#?}", err))
                .unwrap();

            continue;
        }

        // Case 2
        // when have prev candle and update new candle base on prev candle to create
        if let Some(candle) = pre_candle.clone().filter(|c| c.timestamp != resolution_timestamp) {
            price_low = price.min(candle.price.close.parse::<f64>().unwrap_or(price));
            price_high = price_high.max(candle.price.close.parse::<f64>().unwrap_or(price_high));
            price_open = candle.price.close.parse::<f64>().unwrap_or(price_open);

            price_usd_low =
                price_usd.min(candle.price_usd.close.parse::<f64>().unwrap_or(price_usd));
            price_usd_high = price_usd_high.max(
                candle
                    .price_usd
                    .close
                    .parse::<f64>()
                    .unwrap_or(price_usd_high),
            );
            price_usd_open = candle
                .price_usd
                .close
                .parse::<f64>()
                .unwrap_or(price_usd_open);

            market_cap_low =
                market_cap.min(candle.market_cap.close.parse::<f64>().unwrap_or(market_cap));
            market_cap_high = market_cap_high.max(
                candle
                    .market_cap
                    .close
                    .parse::<f64>()
                    .unwrap_or(market_cap_high),
            );
            market_cap_open = candle
                .market_cap
                .close
                .parse::<f64>()
                .unwrap_or(market_cap_open);

            market_cap_usd_low = market_cap_usd.min(
                candle
                    .market_cap_usd
                    .close
                    .parse::<f64>()
                    .unwrap_or(market_cap_usd),
            );
            market_cap_usd_high = market_cap_usd_high.max(
                candle
                    .market_cap_usd
                    .close
                    .parse::<f64>()
                    .unwrap_or(market_cap_usd_high),
            );
            market_cap_usd_open = candle
                .market_cap_usd
                .close
                .parse::<f64>()
                .unwrap_or(market_cap_usd_open);
        }

        // Case 3
        // Not exist candle
        if pre_candle.clone().is_none() {
            tracing::info!("Not exist candle");

            let init_virtual_sol_reserves = bonding_curve_params.initial_virtual_sol_reserves.to_string();
            let init_virtual_token_reserves = bonding_curve_params.initial_virtual_token_reserves.to_string();

            let init_virtual_sol_reserves_without_decimals =
                un_decimals_from_str(&init_virtual_sol_reserves, SOL_DECIMALS);

            let init_virtual_token_reserves_without_decimals =
                un_decimals_from_str(&init_virtual_token_reserves, TOKEN_DECIMALS);

            let init_token_price_sol = init_virtual_sol_reserves_without_decimals
                / init_virtual_token_reserves_without_decimals;

            // sol price usd
            let sol_price_usd = price_usd / price;

            // update low and open price by init price
            price_low = init_token_price_sol;
            price_open = init_token_price_sol;

            price_usd_low = init_token_price_sol * sol_price_usd;
            price_usd_open = init_token_price_sol * sol_price_usd;
        }

        // when new candle crate
        // For 2 case:
        // - Not exist candle (Case 3)
        // - Have prev candle and update new candle (Case 2)
        let new_candle = Candles {
            token_address: base_token.clone(),
            timestamp: resolution_timestamp,
            resolution: resolution as u32,
            price: Candle {
                low: price_low.to_string(),
                high: price_high.to_string(),
                open: price_open.to_string(),
                close: price.to_string(),
            },
            price_usd: Candle {
                low: price_usd_low.to_string(),
                high: price_usd_high.to_string(),
                open: price_usd_open.to_string(),
                close: price_usd.to_string(),
            },
            market_cap: Candle {
                low: market_cap_low.to_string(),
                high: market_cap_high.to_string(),
                open: market_cap_open.to_string(),
                close: market_cap.to_string(),
            },
            market_cap_usd: Candle {
                low: market_cap_usd_low.to_string(),
                high: market_cap_usd_high.to_string(),
                open: market_cap_usd_open.to_string(),
                close: market_cap_usd.to_string(),
            },
            volume_quote: volume_quote.to_string(),
            volume_base: volume_base.to_string(),
            volume_usd: volume_usd.to_string(),
        };

        match CANDLES_REPOSITORY
            .insert_one_or_ignore(&new_candle, None)
            .await
        {
            Ok(_) => continue,
            Err(err) => {
                return Err(err);
            }
        }
    }

    Ok(())
}

#[allow(clippy::too_many_arguments)]
pub async fn handle_update_candle_per_swap(
    base_token: String,
    quote_token: String,
    timestamp: u64,
    price: f64,
    price_usd: f64,
    base_amount_without_decimals: f64,
    quote_amount_without_decimals: f64,
) -> anyhow::Result<()> {
    const MAX_RETRY_COUNT: u32 = 5;

    let mut retry_count = 0;
    while retry_count <= MAX_RETRY_COUNT {
        match update_candle_per_swap(
            base_token.clone(),
            quote_token.clone(),
            timestamp,
            price,
            price_usd,
            base_amount_without_decimals,
            quote_amount_without_decimals,
        )
        .await
        {
            Ok(_) => break,
            Err(err) => {
                if !err.to_string().to_lowercase().contains("duplicate key") {
                    tracing::error!("Error when updating candle per swap: {:#?}", err);
                    break;
                }

                retry_count += 1;

                if retry_count == MAX_RETRY_COUNT {
                    tracing::error!(
                        "handle_update_candle_per_swap too many retries, token address: {:?}",
                        base_token
                    );
                }

                tokio::time::sleep(tokio::time::Duration::from_millis(15)).await;
            }
        }
    }

    Ok(())
}
