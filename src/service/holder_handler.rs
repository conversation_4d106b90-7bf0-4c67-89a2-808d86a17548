use bigdecimal::BigDecimal;
use bson::doc;
use std::str::FromStr;

use crate::{
    db::{holders::Holders, shared_traits::RepositorySharedMethod},
    shared::HOLDERS_REPOSITORY,
    types::PubHolders,
    utils::{un_decimals_from_str, ToF64},
};

use super::{
    collect_curve_event::TransactionInfo, common::TOKEN_DECIMALS, redis_service::RedisEmitter,
    trade_handler::TradeEvent,
};

pub async fn handle_trade_event_for_holder(
    event: &TradeEvent,
    transaction_info: TransactionInfo,
    redis_emitter: &RedisEmitter,
) -> anyhow::Result<()> {
    let is_buy = event.is_buy;
    let last_hash = transaction_info.signature_str;
    let last_synced_at = transaction_info.block_time;
    let token_address = event.mint.to_string();
    let user_address = event.user.to_string();
    let un_decimals_amount =
        un_decimals_from_str(&event.token_amount.to_string(), TOKEN_DECIMALS as u32);
    let change_amount = if is_buy {
        un_decimals_amount
    } else {
        -un_decimals_amount
    };

    upsert_holder(
        token_address.clone(),
        user_address.clone(),
        change_amount,
        last_hash.clone(),
        last_synced_at,
    )
    .await?;

    // Send update holder event to ws server
    if let Err(e) = emit_update_holder_event(
        PubHolders {
            change_amount,
            last_hash,
            last_synced_at,
            user_address,
        },
        &token_address,
        redis_emitter,
    ) {
        tracing::error!("Failed to emit to ws server: {}", e);
    }

    Ok(())
}

async fn upsert_holder(
    token_address: String,
    user_address: String,
    change_amount: f64,
    last_hash: String,
    last_synced_at: Option<i64>,
) -> anyhow::Result<()> {
    let result = HOLDERS_REPOSITORY
        .get_collection()
        .find_one(
            doc! {
                "tokenAddress": &token_address,
                "userAddress": &user_address,
            },
            None,
        )
        .await;

    let Ok(opt_holder) = result else {
        tracing::error!(
            "Failed to find holder info for holder={}, token={}: {:?}",
            &token_address,
            &user_address,
            result.err()
        );
        return Ok(());
    };

    let Some(holder) = opt_holder else {
        let new_holder = Holders {
            token_address: token_address.clone(),
            user_address: user_address.clone(),
            amount: change_amount.to_string(),
            last_hash: last_hash.clone(),
            last_synced_at,
        };

        match HOLDERS_REPOSITORY
            .get_collection()
            .insert_one(new_holder, None)
            .await
        {
            Ok(_) => {
                tracing::info!(
                    "Holder info inserted for: holder={}, token={}, amount={}",
                    &token_address,
                    &user_address,
                    change_amount
                );
            }
            Err(e) => {
                tracing::error!(
                    "Failed to insert holder info for holder={}, token={}, amount={}: {}",
                    &token_address,
                    &user_address,
                    change_amount,
                    e
                );
            }
        }
        return Ok(());
    };

    let change_bg_amount = BigDecimal::from_str(&change_amount.to_string()).unwrap();
    let old_bg_amount = BigDecimal::from_str(&holder.amount.to_string()).unwrap();
    let new_amount = (old_bg_amount + change_bg_amount).to_f64();

    let result = HOLDERS_REPOSITORY
        .get_collection()
        .update_one(
            doc! {
                "tokenAddress": &token_address,
                "userAddress": &user_address,
            },
            doc! {
                "$set": {
                    "amount": &new_amount.to_string(),
                    "lastHash": &last_hash,
                    "lastSyncedAt": &last_synced_at,
                }
            },
            None,
        )
        .await;

    match result {
        Ok(_) => {
            tracing::info!(
                "Holder info updated for: holder={}, token={}, amount={}",
                &token_address,
                &user_address,
                new_amount
            );
        }
        Err(e) => {
            tracing::error!(
                "Failed to update holder info for holder={}, token={}, amount={}: {}",
                &token_address,
                &user_address,
                new_amount,
                e
            );
        }
    }

    Ok(())
}

fn emit_update_holder_event(
    holder: PubHolders,
    token_address: &str,
    redis_emitter: &RedisEmitter,
) -> anyhow::Result<()> {
    let pub_data = serde_json::to_string(&holder)?;
    let room = format!("SUBSCRIBE_COIN::{}", token_address);
    let event = "UpdatedHolder";

    redis_emitter.emit_room(&room, event, &pub_data);

    Ok(())
}
