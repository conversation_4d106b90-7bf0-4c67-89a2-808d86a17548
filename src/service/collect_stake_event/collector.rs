use std::time::Duration;

use anchor_lang::prelude::event::EVENT_IX_TAG_LE;
use anchor_lang::{prelude::*, Discriminator};
use bson::Document;
use futures::future::try_join_all;
use solana_client::{
    rpc_client::GetConfirmedSignaturesForAddress2Config, rpc_config::RpcTransactionConfig,
};
use solana_sdk::commitment_config::CommitmentConfig;
use solana_sdk::{pubkey::Pubkey, signature::Signature};
use solana_transaction_status::EncodedConfirmedTransactionWithStatusMeta;
use solana_transaction_status::{
    option_serializer::OptionSerializer, UiInnerInstructions, UiInstruction, UiTransactionEncoding,
};
use tokio::time::sleep;

use crate::db::latest_signatures::EServiceName;
use crate::db::shared_traits::RepositorySharedMethod;
use crate::db::staking_events::{EStakingEventType, StakingEvents};
use crate::service::redis_service::RedisEmitter;
use crate::shared::{LATEST_SIGNATURES_REPOSITORY, STAKING_EVENTS_REPOSITORY};
use crate::{config::APP_CONFIG, shared::SOLANA_CLIENT};

use super::events::{
    ClaimCreatorPoolEvent, ClaimStakingPoolEvent, DepositCreatorPoolEvent,
    InitializeCreatorPoolEvent, InitializeStakingPoolEvent, StakeEvent, UnstakeEvent,
    UpdateRewardIndexEvent,
};

pub async fn collect_stake_event() -> anyhow::Result<()> {
    let redis_emitter = RedisEmitter::new()?;
    let program_id: Pubkey = APP_CONFIG.stake_program_id.parse()?;
    let mut latest_signature = LATEST_SIGNATURES_REPOSITORY
        .find_one_by_service_name(&EServiceName::CollectStakeEvent)
        .await?
        .and_then(|record| record.signature.parse().ok());

    const SLEEP_DURATION: Duration = Duration::from_millis(100);
    const MAX_BATCH_SIZE: usize = 10;

    loop {
        let signatures = fetch_signatures_until(&program_id, latest_signature).await?;
        if signatures.is_empty() {
            sleep(SLEEP_DURATION).await;
            continue;
        }

        for batch in signatures.chunks(MAX_BATCH_SIZE) {
            let signature_transactions = try_join_all(batch.iter().map(|signature| async move {
                let transaction = SOLANA_CLIENT
                    .get_transaction_with_config(
                        signature,
                        RpcTransactionConfig {
                            encoding: Some(UiTransactionEncoding::Base64),
                            commitment: Some(CommitmentConfig::confirmed()),
                            ..Default::default()
                        },
                    )
                    .await?;
                Ok::<_, anyhow::Error>((signature, transaction))
            }))
            .await?;

            for (signature, transaction) in signature_transactions {
                each_signature(signature, &transaction, &redis_emitter).await?;
                LATEST_SIGNATURES_REPOSITORY
                    .upsert_latest_signature(
                        &EServiceName::CollectStakeEvent,
                        &signature.to_string(),
                    )
                    .await?;
            }
        }

        latest_signature = signatures.last().copied();
        sleep(SLEEP_DURATION).await;
    }
}

async fn each_signature(
    signature: &Signature,
    transaction: &EncodedConfirmedTransactionWithStatusMeta,
    redis_emitter: &RedisEmitter,
) -> anyhow::Result<()> {
    let slot = transaction.slot;
    let Some(metadata) = &transaction.transaction.meta else {
        return Ok(());
    };
    if metadata.err.is_some() {
        return Ok(());
    };
    let OptionSerializer::Some(inner_instructions) = metadata.inner_instructions.as_ref() else {
        return Ok(());
    };
    for (inner_index, inner_instruction) in inner_instructions.iter().enumerate() {
        each_inner_instructions(
            inner_index,
            inner_instruction,
            redis_emitter,
            TransactionInfo {
                block_time: transaction.block_time,
                signature_str: signature.to_string(),
                slot,
            },
        )
        .await?;
    }

    Ok(())
}

async fn each_inner_instructions(
    inner_index: usize,
    ix: &UiInnerInstructions,
    _redis_emitter: &RedisEmitter,
    transaction_info: TransactionInfo,
) -> anyhow::Result<()> {
    let instructions = &ix.instructions;
    for instruction in instructions {
        let UiInstruction::Compiled(compiled_ix) = instruction else {
            continue;
        };
        if compiled_ix.data.is_empty() {
            continue;
        }

        let raw_data = bs58::decode(compiled_ix.data.as_str()).into_vec()?;

        let mut data: Option<Document> = None;
        let mut event_type: Option<EStakingEventType> = None;

        if let Some(event) = parse_event_cpi::<ClaimCreatorPoolEvent>(&raw_data) {
            tracing::info!("{} {:?}", inner_index, event);
            data = Some(bson::to_document(&event)?);
            event_type = Some(EStakingEventType::ClaimCreatorPoolEvent);
        }

        if let Some(event) = parse_event_cpi::<ClaimStakingPoolEvent>(&raw_data) {
            tracing::info!("{} {:?}", inner_index, event);
            data = Some(bson::to_document(&event)?);
            event_type = Some(EStakingEventType::ClaimStakingPoolEvent);
        }

        if let Some(event) = parse_event_cpi::<DepositCreatorPoolEvent>(&raw_data) {
            tracing::info!("{} {:?}", inner_index, event);
            data = Some(bson::to_document(&event)?);
            event_type = Some(EStakingEventType::DepositCreatorPoolEvent);
        }

        if let Some(event) = parse_event_cpi::<InitializeCreatorPoolEvent>(&raw_data) {
            tracing::info!("{} {:?}", inner_index, event);
            data = Some(bson::to_document(&event)?);
            event_type = Some(EStakingEventType::InitializeCreatorPoolEvent);
        }

        if let Some(event) = parse_event_cpi::<InitializeStakingPoolEvent>(&raw_data) {
            tracing::info!("{} {:?}", inner_index, event);
            data = Some(bson::to_document(&event)?);
            event_type = Some(EStakingEventType::InitializeStakingPoolEvent);
        }

        if let Some(event) = parse_event_cpi::<StakeEvent>(&raw_data) {
            tracing::info!("{} {:?}", inner_index, event);
            data = Some(bson::to_document(&event)?);
            event_type = Some(EStakingEventType::StakeEvent);
        }

        if let Some(event) = parse_event_cpi::<UnstakeEvent>(&raw_data) {
            tracing::info!("{} {:?}", inner_index, event);
            data = Some(bson::to_document(&event)?);
            event_type = Some(EStakingEventType::UnstakeEvent);
        }

        if let Some(event) = parse_event_cpi::<UpdateRewardIndexEvent>(&raw_data) {
            tracing::info!("{} {:?}", inner_index, event);
            data = Some(bson::to_document(&event)?);
            event_type = Some(EStakingEventType::UpdateRewardIndexEvent);
        }

        if data.is_none() || event_type.is_none() {
            continue;
        }

        let event = StakingEvents {
            slot: transaction_info.slot,
            index: inner_index as i64,
            signature: transaction_info.signature_str.clone(),
            block_time: transaction_info.block_time,
            event: event_type.unwrap(),
            data: data.unwrap(),
        };

        match STAKING_EVENTS_REPOSITORY
            .insert_one(event.clone(), None)
            .await
        {
            Ok(_) => {
                tracing::info!("Inserted staking event: {:?}", event);
            }
            Err(err) => {
                tracing::error!("Failed to insert staking event: {:?}", err);
            }
        }
    }
    Ok(())
}

fn parse_event_cpi<E: AnchorDeserialize + Discriminator>(ix_data: &[u8]) -> Option<E> {
    if ix_data.len() < 16 {
        return None;
    }
    if ix_data[..8] == EVENT_IX_TAG_LE {
        let event_cpi = &ix_data[8..];
        let event_discriminator = &event_cpi[..8];
        if event_discriminator.eq(&E::discriminator()) {
            let event = E::try_from_slice(&event_cpi[8..]);
            return event.ok();
        }
    }
    None
}

/// old to new
/// until is exclusive
async fn fetch_signatures_until(
    program_id: &Pubkey,
    until: Option<Signature>,
) -> anyhow::Result<Vec<Signature>> {
    let mut before = None;
    let client = &SOLANA_CLIENT;
    let mut result = vec![];

    'outer: loop {
        // new to old
        let signatures = client
            .get_signatures_for_address_with_config(
                program_id,
                GetConfirmedSignaturesForAddress2Config {
                    before,
                    commitment: Some(CommitmentConfig::confirmed()),
                    ..Default::default()
                },
            )
            .await?;
        if signatures.is_empty() {
            break;
        }

        for signature in signatures {
            let signature: Signature = signature.signature.parse()?;

            if Some(signature) == before {
                continue;
            }
            if Some(signature) == until {
                // break parent loop
                break 'outer;
            }

            result.insert(0, signature);
            before = Some(signature);
        }
        sleep(Duration::from_millis(100)).await
    }

    Ok(result)
}

#[derive(Debug, Clone)]
pub struct TransactionInfo {
    pub block_time: Option<i64>,
    pub signature_str: String,
    pub slot: u64,
}
