use anchor_lang::prelude::*;
use serde::{Serialize, Serializer};
use solana_sdk::pubkey::Pubkey;

#[event]
#[derive(Debug, Serialize)]
pub struct ClaimCreatorPoolEvent {
    #[serde(serialize_with = "serialize_pubkey")]
    pub stake_mint: Pubkey,
    #[serde(serialize_with = "serialize_pubkey")]
    pub creator: Pubkey,
    #[serde(serialize_with = "serialize_pubkey")]
    pub creator_pool: Pubkey,
    pub reward: u64,
    pub timestamp: i64,
}

#[event]
#[derive(Debug, Serialize)]
pub struct ClaimStakingPoolEvent {
    #[serde(serialize_with = "serialize_pubkey")]
    pub stake_mint: Pubkey,
    #[serde(serialize_with = "serialize_pubkey")]
    pub staking_pool: Pubkey,
    #[serde(serialize_with = "serialize_pubkey")]
    pub claimer: Pubkey,
    pub reward: u64,
    pub timestamp: i64,
}

#[event]
#[derive(Debug, Serialize)]
pub struct DepositCreator<PERSON>oolEvent {
    #[serde(serialize_with = "serialize_pubkey")]
    pub stake_mint: Pubkey,
    #[serde(serialize_with = "serialize_pubkey")]
    pub creator_pool: Pubkey,
    #[serde(serialize_with = "serialize_pubkey")]
    pub depositor: Pubkey,
    pub amount: u64,
    pub timestamp: i64,
}

#[event]
#[derive(Debug, Serialize)]
pub struct InitializeCreatorPoolEvent {
    #[serde(serialize_with = "serialize_pubkey")]
    pub stake_mint: Pubkey,
    #[serde(serialize_with = "serialize_pubkey")]
    pub creator_pool: Pubkey,
    #[serde(serialize_with = "serialize_pubkey")]
    pub creator: Pubkey,
    pub timestamp: i64,
}

#[event]
#[derive(Debug, Serialize)]
pub struct InitializeStakingPoolEvent {
    #[serde(serialize_with = "serialize_pubkey")]
    pub stake_mint: Pubkey,
    #[serde(serialize_with = "serialize_pubkey")]
    pub staking_pool: Pubkey,
    #[serde(serialize_with = "serialize_pubkey")]
    pub creator: Pubkey,
    pub timestamp: i64,
}

#[event]
#[derive(Debug, Serialize)]
pub struct StakeEvent {
    #[serde(serialize_with = "serialize_pubkey")]
    pub stake_mint: Pubkey,
    #[serde(serialize_with = "serialize_pubkey")]
    pub staking_pool: Pubkey,
    #[serde(serialize_with = "serialize_pubkey")]
    pub staker: Pubkey,
    pub amount: u64,
    pub timestamp: i64,
}

#[event]
#[derive(Debug, Serialize)]
pub struct UnstakeEvent {
    #[serde(serialize_with = "serialize_pubkey")]
    pub stake_mint: Pubkey,
    #[serde(serialize_with = "serialize_pubkey")]
    pub staking_pool: Pubkey,
    #[serde(serialize_with = "serialize_pubkey")]
    pub unstaker: Pubkey,
    pub amount: u64,
    pub timestamp: i64,
}

#[event]
#[derive(Debug, Serialize)]
pub struct UpdateRewardIndexEvent {
    #[serde(serialize_with = "serialize_pubkey")]
    pub stake_mint: Pubkey,
    #[serde(serialize_with = "serialize_pubkey")]
    pub staking_pool: Pubkey,
    #[serde(serialize_with = "serialize_pubkey")]
    pub reward_updater: Pubkey,
    pub reward: u64,
    pub timestamp: i64,
}

fn serialize_pubkey<S>(pubkey: &Pubkey, serializer: S) -> std::result::Result<S::Ok, S::Error>
where
    S: Serializer,
{
    serializer.serialize_str(&pubkey.to_string())
}
