use std::str::FromStr;
use std::sync::LazyLock;
use std::time::Duration;

use solana_client::nonblocking::rpc_client::RpcClient;
use solana_sdk::pubkey::Pubkey;

use crate::db::bonding_curve_error::BondingCurveErrorsRepository;
use crate::db::staking_events::StakingEventsRepository;
use crate::db::{
    candles::CandlesRepository, coins::CoinsRepository, common::MONGODB,
    holders::HoldersRepository, latest_signatures::LatestSignaturesRepository,
    params::ParamsRepository, shared_traits::RepositorySharedMethod,
    token_prices::TokenPricesRepository, trades::TradesRepository,
};

pub static WSOL_PUBKEY: LazyLock<Pubkey> =
    LazyLock::new(|| Pubkey::from_str("So11111111111111111111111111111111111111112").unwrap());

use crate::config::APP_CONFIG;

pub static SOLANA_CLIENT: LazyLock<RpcClient> =
    LazyLock::new(|| RpcClient::new(APP_CONFIG.fullnode_url.to_string()));

pub static TRADES_REPOSITORY: LazyLock<TradesRepository> =
    LazyLock::new(|| TradesRepository::new(MONGODB.get().unwrap()));

pub static CANDLES_REPOSITORY: LazyLock<CandlesRepository> =
    LazyLock::new(|| CandlesRepository::new(MONGODB.get().unwrap()));
pub static HOLDERS_REPOSITORY: LazyLock<HoldersRepository> =
    LazyLock::new(|| HoldersRepository::new(MONGODB.get().unwrap()));
pub static COINS_REPOSITORY: LazyLock<CoinsRepository> =
    LazyLock::new(|| CoinsRepository::new(MONGODB.get().unwrap()));
pub static PARAMS_REPOSITORY: LazyLock<ParamsRepository> =
    LazyLock::new(|| ParamsRepository::new(MONGODB.get().unwrap()));
pub static TOKEN_PRICES_REPOSITORY: LazyLock<TokenPricesRepository> =
    LazyLock::new(|| TokenPricesRepository::new(MONGODB.get().unwrap()));
pub static LATEST_SIGNATURES_REPOSITORY: LazyLock<LatestSignaturesRepository> =
    LazyLock::new(|| LatestSignaturesRepository::new(MONGODB.get().unwrap()));
pub static STAKING_EVENTS_REPOSITORY: LazyLock<StakingEventsRepository> =
    LazyLock::new(|| StakingEventsRepository::new(MONGODB.get().unwrap()));

pub static BONDING_CURVE_ERRORS_REPOSITORY: LazyLock<BondingCurveErrorsRepository> =
    LazyLock::new(|| BondingCurveErrorsRepository::new(MONGODB.get().unwrap()));

pub static TIMEOUT_SOLANA_CLIENT: LazyLock<RpcClient> = LazyLock::new(|| {
    RpcClient::new_with_timeout(APP_CONFIG.fullnode_url.to_string(), Duration::from_secs(5))
});

pub static BACKUP_TIMEOUT_SOLANA_CLIENT: LazyLock<RpcClient> = LazyLock::new(|| {
    RpcClient::new_with_timeout(
        APP_CONFIG.fullnode_backup_url.to_string(),
        Duration::from_secs(5),
    )
});
