use bson::{oid::ObjectId, DateTime};
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct LatestSignatures {
    #[serde(rename = "_id")]
    #[serde(skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub service_name: EServiceName,
    pub signature: String,
    pub updated_at: DateTime,
}

#[derive(Debug, Serialize, Deserialize, strum_macros::Display, PartialEq, Clone)]
#[serde(rename_all = "SCREAMING_SNAKE_CASE")]
#[strum(serialize_all = "SCREAMING_SNAKE_CASE")]
pub enum EServiceName {
    CollectCurveEvent,
    CollectStakeEvent,
}
