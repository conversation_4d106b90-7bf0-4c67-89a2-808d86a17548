use mongodb::{Collection, Database};

use crate::db::{share::EDatabase, shared_traits::RepositorySharedMethod};

use super::Holders;

#[derive(Debug, Clone)]
pub struct HoldersRepository {
    collection: Collection<Holders>,
}

impl RepositorySharedMethod for HoldersRepository {
    type Schema = Holders;

    fn new(database: &Database) -> Self {
        Self {
            collection: database.collection::<Self::Schema>(&EDatabase::Holders.to_string()),
        }
    }

    fn get_collection(&self) -> &Collection<Self::Schema> {
        &self.collection
    }
}

impl HoldersRepository {}
