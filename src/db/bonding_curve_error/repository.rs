use mongodb::{options::InsertOneOptions, Collection, Database};

use crate::db::{
    bonding_curve_error::BondingCurveErrors, share::EDatabase,
    shared_traits::RepositorySharedMethod,
};

#[derive(<PERSON>bu<PERSON>, <PERSON>lone)]
pub struct BondingCurveErrorsRepository {
    collection: Collection<BondingCurveErrors>,
}

impl RepositorySharedMethod for BondingCurveErrorsRepository {
    type Schema = BondingCurveErrors;

    fn new(database: &Database) -> Self {
        Self {
            collection: database
                .collection::<Self::Schema>(&EDatabase::BondingCurveErrors.to_string()),
        }
    }

    fn get_collection(&self) -> &Collection<Self::Schema> {
        &self.collection
    }
}

impl BondingCurveErrorsRepository {
    pub async fn create(&self, data: BondingCurveErrors) -> anyhow::Result<()> {
        self.collection
            .insert_one(data, InsertOneOptions::default())
            .await?;
        Ok(())
    }
}
