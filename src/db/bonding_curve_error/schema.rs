use bson::{DateTime, Decimal128};
use serde::{Deserialize, Serialize};
use std::fmt::Debug;

#[derive(Serialize, Debug, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub struct CompleteEventError {
    pub user: String,
    pub mint: String,
    pub bonding_curve: String,
    pub timestamp: i64,
    pub sol_amount: u64,
    pub token_amount: u64,
}

#[derive(Serialize, Debug, Deserialize, Clone)]
#[serde(rename_all = "camelCase")]
pub struct BondingCurveErrors {
    pub step: String,
    pub error: String,
    pub token_address: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub event_data: Option<CompleteEventError>,
    pub input_pc_amount: Decimal128,
    pub input_sol_amount: Decimal128,
    pub initial_price: Decimal128,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub listing_pair_id: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub create_pool_signature: Option<String>,
    pub created_at: DateTime,
    pub updated_at: DateTime,
    #[serde(default)]
    pub is_fixed: bool,
}
