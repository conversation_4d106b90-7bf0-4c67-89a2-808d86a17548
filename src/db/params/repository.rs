use std::str::FromStr;

use bson::{doc, Decimal128};
use mongodb::{options::FindOneAndUpdateOptions, Collection, Database};

use crate::{
    db::{share::EDatabase, shared_traits::RepositorySharedMethod},
    service::collect_curve_event::SetParamsEvent,
};

use super::Params;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct ParamsRepository {
    collection: Collection<Params>,
}

impl RepositorySharedMethod for ParamsRepository {
    type Schema = Params;

    fn new(database: &Database) -> Self {
        Self {
            collection: database.collection::<Self::Schema>(&EDatabase::Params.to_string()),
        }
    }

    fn get_collection(&self) -> &Collection<Self::Schema> {
        &self.collection
    }
}

impl ParamsRepository {
    pub async fn upsert_params_from_set_params_event(
        &self,
        event: SetParamsEvent,
    ) -> anyhow::Result<()> {
        let result = self
            .collection
            .find_one_and_update(
                doc! {},
                doc! {
                    "$set": {
                        "feeRecipient": event.fee_recipient.to_string(),
                        "initialVirtualTokenReserves": Decimal128::from_str(&event.initial_virtual_token_reserves.to_string()).unwrap(),
                        "initialVirtualSolReserves": Decimal128::from_str(&event.initial_virtual_sol_reserves.to_string()).unwrap(),
                        "initialRealTokenReserves": Decimal128::from_str(&event.initial_real_token_reserves.to_string()).unwrap(),
                        "tokenTotalSupply": bson::to_bson(&event.token_total_supply)?,
                        "feeBasisPoints": bson::to_bson(&event.fee_basis_points)?,
                    }
                },
                FindOneAndUpdateOptions::builder().upsert(true).build(),
            )
            .await;

        match result {
            Ok(_) => {
                tracing::info!("Params updated: {:?}", event);
            }
            Err(e) => {
                tracing::error!("Failed to update database for params: {}", e);
            }
        }

        Ok(())
    }
}
