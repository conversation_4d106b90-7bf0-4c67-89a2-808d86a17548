use bson::Decimal128;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct Params {
    pub fee_recipient: String,
    pub initial_virtual_token_reserves: Decimal128,
    pub initial_virtual_sol_reserves: Decimal128,
    pub initial_real_token_reserves: Decimal128,
    pub token_total_supply: u64,
    pub fee_basis_points: u64,
}
