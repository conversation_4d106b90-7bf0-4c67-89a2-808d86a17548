use mongodb::{Collection, Database};

use crate::db::{share::EDatabase, shared_traits::RepositorySharedMethod};

use super::Candles;

#[derive(Debug, Clone)]
pub struct CandlesRepository {
    collection: Collection<Candles>,
}

impl RepositorySharedMethod for CandlesRepository {
    type Schema = Candles;

    fn new(database: &Database) -> Self {
        Self {
            collection: database.collection::<Self::Schema>(&EDatabase::Candles.to_string()),
        }
    }

    fn get_collection(&self) -> &Collection<Self::Schema> {
        &self.collection
    }
}

impl CandlesRepository {}

#[derive(Debug, <PERSON>lone, Copy)]
pub enum QueryTimeFrame {
    FiveMinutes,
    OneHour,
    SixHours,
    TwentyFourHours,
}
