use bson::Decimal128;
use serde::{Deserialize, Serialize};
use strum_macros::{EnumString, VariantNames};

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct Trades {
    pub slot: u64,
    pub hash: String,
    pub token_address: String,
    pub index: u64,
    pub maker: String,
    pub trade_type: ETradeType,
    pub base_amount: String,
    pub quote_amount: String,
    pub price: String,
    pub price_usd: String,
    pub volume: String,
    pub volume_usd: String,
    pub timestamp: u64,
    pub virtual_sol_reserves: Decimal128,
    pub virtual_token_reserves: Decimal128,
    pub real_sol_reserves: Decimal128,
    pub real_token_reserves: Decimal128,
    pub token_symbol: Option<String>,
    pub creator_sol_fee: String,
    pub creator_usd_fee: String,
    pub airdrop_sol_fee: String,
    pub airdrop_usd_fee: String,
}

#[derive(
    Debug,
    Serialize,
    Deserialize,
    <PERSON>lone,
    Copy,
    EnumString,
    VariantNames,
    strum_macros::Display,
    PartialEq,
    Eq,
    Hash,
)]
#[serde(rename_all = "SCREAMING_SNAKE_CASE")]
#[strum(serialize_all = "SCREAMING_SNAKE_CASE")]
pub enum ETradeType {
    Buy,
    Sell,
}
