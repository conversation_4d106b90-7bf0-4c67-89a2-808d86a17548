use bson::Document;
use serde::{Deserialize, Serialize};
use strum_macros::{EnumString, VariantNames};

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct StakingEvents {
    pub slot: u64,
    pub index: i64,
    pub signature: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub block_time: Option<i64>,
    pub event: EStakingEventType,
    pub data: Document,
}

#[derive(
    Debug,
    Serialize,
    Deserialize,
    Clone,
    Copy,
    EnumString,
    VariantNames,
    strum_macros::Display,
    PartialEq,
    Eq,
    Hash,
)]
#[serde(rename_all = "SCREAMING_SNAKE_CASE")]
#[strum(serialize_all = "SCREAMING_SNAKE_CASE")]
pub enum EStakingEventType {
    ClaimCreatorPoolEvent,
    ClaimStakingPoolEvent,
    DepositCreatorPoolEvent,
    InitializeCreatorPoolEvent,
    InitializeStakingPoolEvent,
    StakeEvent,
    UnstakeEvent,
    UpdateRewardIndexEvent,
}
