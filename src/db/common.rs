use bson::doc;
use mongodb::options::ClientOptions;
use mongodb::{Client, Database};
use tokio::sync::OnceCell;

pub static MONGODB: OnceCell<Database> = OnceCell::const_new();

pub async fn setup_mongodb(uri: &str) -> &'static Database {
    MONGODB
        .get_or_init(|| async {
            let client_options = ClientOptions::parse(uri).await.unwrap();

            let database_name = client_options
                .default_database
                .as_ref()
                .expect("Database name must be included in MongoDB URI")
                .to_string();
            let client = Client::with_options(client_options).unwrap();

            let database_instance = client.database(&database_name);
            database_instance
                .run_command(doc! { "ping": 1 }, None)
                .await
                .unwrap();
            database_instance
        })
        .await
}
