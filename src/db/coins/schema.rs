use bson::{DateTime, Decimal128};
use serde::{Deserialize, Serialize};
use strum_macros::{EnumString, VariantNames};

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct Coins {
    pub token_address: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub bonding_curve_address: Option<String>,
    pub name: String,
    pub symbol: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub description: Option<String>,
    pub logo_uri: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub socials: Option<Vec<Social>>,
    pub creator_address: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub reply_count: Option<String>,
    pub mcap: Decimal128,
    pub mcap_usd: Decimal128,
    pub virtual_sol_reserves: Decimal128,
    pub virtual_token_reserves: Decimal128,
    pub real_sol_reserves: Decimal128,
    pub real_token_reserves: Decimal128,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub prev_mcap: Option<Decimal128>,
    pub bonding_curve: f32,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub listing_at: Option<DateTime>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub listing_pair_id: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub listed_status: Option<EListingStatusType>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub initial_price: Option<f64>,
    pub created_price: Option<f64>,
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct Social {
    pub name: String,
    pub url: String,
}

#[derive(
    Debug,
    Serialize,
    Deserialize,
    Clone,
    Copy,
    EnumString,
    VariantNames,
    strum_macros::Display,
    PartialEq,
    Eq,
    Hash,
)]
#[serde(rename_all = "SCREAMING_SNAKE_CASE")]
#[strum(serialize_all = "SCREAMING_SNAKE_CASE")]
pub enum EListingStatusType {
    Migrating,
    Migrated,
    Unmigrated,
}
