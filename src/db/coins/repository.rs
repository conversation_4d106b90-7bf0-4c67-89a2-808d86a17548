use bson::{doc, DateTime};
use mongodb::{Collection, Database};

use crate::db::{
    coins::EListingStatusType, share::EDatabase, shared_traits::RepositorySharedMethod,
};

use super::Coins;

#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct CoinsRepository {
    collection: Collection<Coins>,
}

impl RepositorySharedMethod for CoinsRepository {
    type Schema = Coins;

    fn new(database: &Database) -> Self {
        Self {
            collection: database.collection::<Self::Schema>(&EDatabase::Coins.to_string()),
        }
    }

    fn get_collection(&self) -> &Collection<Self::Schema> {
        &self.collection
    }
}

impl CoinsRepository {
    pub async fn complete_bonding_curve(
        &self,
        mint: &str,
        raydium_pool_id: &str,
        initial_price: f64,
    ) -> anyhow::Result<()> {
        let filter = doc! { "tokenAddress": mint };
        let update = doc! { "$set": { "listingPairId": raydium_pool_id,
        "listedStatus": EListingStatusType::Migrated.to_string(), "listingAt": DateTime::now(), "initialPrice": initial_price } };
        self.update_one(filter, update, None).await?;
        Ok(())
    }

    pub async fn get_token_by_address(&self, address: &str) -> anyhow::Result<Option<Coins>> {
        let filter = doc! { "tokenAddress": address };
        let coin = self.find_one(filter, None).await?;
        Ok(coin)
    }
}
