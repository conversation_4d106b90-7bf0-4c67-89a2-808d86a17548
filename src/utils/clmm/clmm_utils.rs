use anchor_spl::token_2022::spl_token_2022::{extension::StateWithExtensions, state::Mint};
use anyhow::Result;
use raydium_amm_v3::libraries::{liquidity_math, tick_math};
use solana_account_decoder::{
    parse_token::{TokenAccountType, UiAccountState},
    UiAccountData,
};
use solana_client::{nonblocking::rpc_client::RpcClient, rpc_request::TokenAccountsFilter};
use solana_sdk::pubkey::Pubkey;

use crate::utils::{
    amount_with_slippage, clmm_math, clmm_types::RewardItem, get_anchor_account,
    get_pool_mints_inverse_fee,
};

use super::clmm_types::{ClmmCreatePoolResult, ClmmLiquidityChangeResult};

pub async fn create_pool_price(
    rpc_client: &RpcClient,
    mint0: Pubkey,
    mint1: Pubkey,
    price: f64,
) -> Result<ClmmCreatePoolResult> {
    let mut price = price;
    let mut mint0 = mint0;
    let mut mint1 = mint1;
    if mint0 > mint1 {
        std::mem::swap(&mut mint0, &mut mint1);
        price = 1.0 / price;
    }
    let load_pubkeys = vec![mint0, mint1];
    let rsps = rpc_client
        .get_multiple_accounts(&load_pubkeys)
        .await
        .unwrap();
    let mint0_token_program = rsps[0].as_ref().unwrap().owner;
    let mint1_token_program = rsps[1].as_ref().unwrap().owner;
    let mint0_info = unpack_mint(&rsps[0].as_ref().unwrap().data).unwrap();
    let mint1_info = unpack_mint(&rsps[1].as_ref().unwrap().data).unwrap();
    let sqrt_price_x64 = clmm_math::price_to_sqrt_price_x64(
        price,
        mint0_info.base.decimals,
        mint1_info.base.decimals,
    );
    let tick = tick_math::get_tick_at_sqrt_price(sqrt_price_x64).unwrap();
    Ok(ClmmCreatePoolResult {
        mint0,
        mint1,
        mint0_token_program,
        mint1_token_program,
        price,
        sqrt_price_x64,
        tick,
    })
}

pub fn unpack_mint(token_data: &[u8]) -> Result<StateWithExtensions<Mint>> {
    let mint = StateWithExtensions::<Mint>::unpack(token_data)?;
    Ok(mint)
}

#[allow(clippy::too_many_arguments)]
pub async fn calculate_liquidity_change(
    rpc_client: &RpcClient,
    pool_id: Pubkey,
    tick_lower_price: f64,
    tick_upper_price: f64,
    input_amount: u64,
    slippage_bps: u64,
    collect_reward: bool,
    is_base_0: bool,
) -> Result<ClmmLiquidityChangeResult> {
    let pool = get_anchor_account::<raydium_amm_v3::states::PoolState>(rpc_client, &pool_id)
        .await?
        .unwrap();
    let tick_spacing = pool.tick_spacing;

    let mut load_pubkeys = vec![pool.token_mint_0, pool.token_mint_1];

    let mut reward_items: Vec<RewardItem> = Vec::new();
    if collect_reward {
        // collect reward info while decrease liquidity
        for item in pool.reward_infos.iter() {
            if item.token_mint != Pubkey::default() {
                reward_items.push(RewardItem {
                    token_program: Pubkey::default(),
                    reward_mint: item.token_mint,
                    reward_vault: item.token_vault,
                });
                load_pubkeys.push(item.token_mint);
            }
        }
    }
    let mut rsps = rpc_client
        .get_multiple_accounts(&load_pubkeys)
        .await
        .unwrap();
    let mint0_token_program = rsps.remove(0).unwrap().owner;
    let mint1_token_program = rsps.remove(0).unwrap().owner;
    for (item, rsp) in reward_items.iter_mut().zip(rsps.iter()) {
        item.token_program = rsp.as_ref().unwrap().owner;
    }

    let tick_lower_price_x64 = clmm_math::price_to_sqrt_price_x64(
        tick_lower_price,
        pool.mint_decimals_0,
        pool.mint_decimals_1,
    );

    let tick_upper_price_x64 = clmm_math::price_to_sqrt_price_x64(
        tick_upper_price,
        pool.mint_decimals_0,
        pool.mint_decimals_1,
    );

    let tick_lower_index = clmm_math::tick_with_spacing(
        tick_math::get_tick_at_sqrt_price(tick_lower_price_x64)?,
        tick_spacing.into(),
    );
    let tick_upper_index = clmm_math::tick_with_spacing(
        tick_math::get_tick_at_sqrt_price(tick_upper_price_x64)?,
        tick_spacing.into(),
    );
    let tick_lower_price_x64 = tick_math::get_sqrt_price_at_tick(tick_lower_index)?;
    let tick_upper_price_x64 = tick_math::get_sqrt_price_at_tick(tick_upper_index)?;
    let liquidity = if is_base_0 {
        liquidity_math::get_liquidity_from_single_amount_0(
            pool.sqrt_price_x64,
            tick_lower_price_x64,
            tick_upper_price_x64,
            input_amount,
        )
    } else {
        liquidity_math::get_liquidity_from_single_amount_1(
            pool.sqrt_price_x64,
            tick_lower_price_x64,
            tick_upper_price_x64,
            input_amount,
        )
    };
    let (amount_0, amount_1) = liquidity_math::get_delta_amounts_signed(
        pool.tick_current,
        pool.sqrt_price_x64,
        tick_lower_index,
        tick_upper_index,
        liquidity as i128,
    )?;
    // calc with slippage
    let amount_0_with_slippage = amount_with_slippage(amount_0, slippage_bps, true)?;
    let amount_1_with_slippage = amount_with_slippage(amount_1, slippage_bps, true)?;
    // calc with transfer_fee
    let transfer_fee = get_pool_mints_inverse_fee(
        rpc_client,
        pool.token_mint_0,
        pool.token_mint_1,
        amount_0_with_slippage,
        amount_1_with_slippage,
    )
    .await;
    let amount_0_max = amount_0_with_slippage
        .checked_add(transfer_fee.0.transfer_fee)
        .unwrap();
    let amount_1_max = amount_1_with_slippage
        .checked_add(transfer_fee.1.transfer_fee)
        .unwrap();

    let tick_array_lower_start_index =
        raydium_amm_v3::states::TickArrayState::get_array_start_index(
            tick_lower_index,
            tick_spacing,
        );
    let tick_array_upper_start_index =
        raydium_amm_v3::states::TickArrayState::get_array_start_index(
            tick_upper_index,
            tick_spacing,
        );
    Ok(ClmmLiquidityChangeResult {
        mint0: pool.token_mint_0,
        mint1: pool.token_mint_1,
        vault0: pool.token_vault_0,
        vault1: pool.token_vault_1,
        mint0_token_program,
        mint1_token_program,
        reward_items,
        liquidity,
        amount_0: amount_0_max,
        amount_1: amount_1_max,
        tick_lower_index,
        tick_upper_index,
        tick_array_lower_start_index,
        tick_array_upper_start_index,
    })
}

pub async fn get_nft_accounts_and_positions_by_owner(
    client: &RpcClient,
    owner: &Pubkey,
    raydium_amm_v3_program: &Pubkey,
) -> (Vec<TokenInfo>, Vec<Pubkey>) {
    let mut nft_accounts_info =
        get_nft_accounts_by_owner_with_specified_program(client, owner, spl_token::id()).await;
    let spl_2022_nfts =
        get_nft_accounts_by_owner_with_specified_program(client, owner, spl_token_2022::id()).await;
    nft_accounts_info.extend(spl_2022_nfts);
    let user_position_account: Vec<Pubkey> = nft_accounts_info
        .iter()
        .map(|&nft| {
            Pubkey::find_program_address(
                &[
                    raydium_amm_v3::states::POSITION_SEED.as_bytes(),
                    nft.mint.to_bytes().as_ref(),
                ],
                raydium_amm_v3_program,
            )
            .0
        })
        .collect();
    (nft_accounts_info, user_position_account)
}

pub async fn get_nft_accounts_by_owner_with_specified_program(
    client: &RpcClient,
    owner: &Pubkey,
    token_program: Pubkey,
) -> Vec<TokenInfo> {
    let all_tokens = client
        .get_token_accounts_by_owner(owner, TokenAccountsFilter::ProgramId(spl_token::id()))
        .await
        .unwrap();
    let mut nft_accounts_info = Vec::new();
    for keyed_account in all_tokens {
        if let UiAccountData::Json(parsed_account) = keyed_account.account.data {
            if parsed_account.program == "spl-token" || parsed_account.program == "spl-token-2022" {
                if let Ok(TokenAccountType::Account(ui_token_account)) =
                    serde_json::from_value(parsed_account.parsed)
                {
                    let _frozen = ui_token_account.state == UiAccountState::Frozen;

                    let token = ui_token_account
                        .mint
                        .parse::<Pubkey>()
                        .unwrap_or_else(|err| panic!("Invalid mint: {}", err));
                    let token_account = keyed_account
                        .pubkey
                        .parse::<Pubkey>()
                        .unwrap_or_else(|err| panic!("Invalid token account: {}", err));
                    let token_amount = ui_token_account
                        .token_amount
                        .amount
                        .parse::<u64>()
                        .unwrap_or_else(|err| panic!("Invalid token amount: {}", err));

                    let _close_authority = ui_token_account.close_authority.map_or(*owner, |s| {
                        s.parse::<Pubkey>()
                            .unwrap_or_else(|err| panic!("Invalid close authority: {}", err))
                    });

                    if ui_token_account.token_amount.decimals == 0 && token_amount == 1 {
                        nft_accounts_info.push(TokenInfo {
                            key: token_account,
                            mint: token,
                            program: token_program,
                            amount: token_amount,
                            decimals: ui_token_account.token_amount.decimals,
                        });
                    }
                }
            }
        }
    }
    nft_accounts_info
}

#[derive(Clone, Copy, Debug, PartialEq, Eq)]
pub struct TokenInfo {
    pub key: Pubkey,
    pub mint: Pubkey,
    pub program: Pubkey,
    pub amount: u64,
    pub decimals: u8,
}
