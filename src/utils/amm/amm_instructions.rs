use anyhow::Result;
use solana_sdk::{instruction::Instruction, pubkey::Pubkey};

use super::amm_types::AmmKeys;

#[allow(clippy::too_many_arguments)]
pub fn initialize_amm_pool(
    amm_program: &Pubkey,
    amm_keys: &AmmKeys,
    create_fee_detination: &Pubkey,
    user_owner: &Pubkey,
    user_coin: &Pubkey,
    user_pc: &Pubkey,
    user_lp: &Pubkey,
    open_time: u64,   // default is 0, or set a future time on the chain can start swap
    pc_amount: u64,   // transfer pc asset to the pool pc vault as pool init vault
    coin_amount: u64, // transfer coin asset to the pool coin vault as pool init vault
) -> Result<Instruction> {
    let amm_pool_init_instruction = raydium_amm::instruction::initialize2(
        amm_program,
        &amm_keys.amm_pool,
        &amm_keys.amm_authority,
        &amm_keys.amm_open_order,
        &amm_keys.amm_lp_mint,
        &amm_keys.amm_coin_mint,
        &amm_keys.amm_pc_mint,
        &amm_keys.amm_coin_vault,
        &amm_keys.amm_pc_vault,
        &amm_keys.amm_target,
        &Pubkey::find_program_address(&[raydium_amm::processor::AMM_CONFIG_SEED], amm_program).0,
        create_fee_detination,
        &amm_keys.market_program,
        &amm_keys.market,
        user_owner,
        user_coin,
        user_pc,
        user_lp,
        amm_keys.nonce,
        open_time,
        pc_amount,
        coin_amount,
    )?;
    Ok(amm_pool_init_instruction)
}
