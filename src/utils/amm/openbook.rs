#![allow(dead_code)]

use anchor_lang::prelude::AccountMeta;
use anyhow::Result;
use serum_dex::instruction::{InitializeMarketInstruction, MarketInstruction};
use solana_client::{nonblocking::rpc_client::RpcClient, rpc_config::RpcSendTransactionConfig};
use solana_sdk::{
    commitment_config::CommitmentConfig,
    instruction::Instruction,
    pubkey::Pubkey,
    signature::{Keypair, Signer},
    system_instruction,
    transaction::Transaction,
};

use crate::shared::SOLANA_CLIENT;

pub async fn list_market(
    program_id: &Pubkey,
    payer: &Keypair,
    coin_mint: &Pubkey,
    pc_mint: &Pubkey,
    coin_lot_size: u64,
    pc_lot_size: u64,
) -> Result<Pubkey> {
    let market = generate_pubkey(&payer.pubkey(), program_id)?;
    let coin_vault = generate_pubkey(&payer.pubkey(), &spl_token::ID)?;
    let pc_coin_vault = generate_pubkey(&payer.pubkey(), &spl_token::ID)?;
    let (vault_owner, vault_signer_nonce) = get_vault_owner_and_nonce(&market.0, program_id)?;

    let vault_tx = Transaction::new_signed_with_payer(
        &create_vaults_ixs(
            &payer.pubkey(),
            &vault_owner,
            &coin_vault,
            &pc_coin_vault,
            coin_mint,
            pc_mint,
        )
        .await?,
        Some(&payer.pubkey()),
        &[payer],
        SOLANA_CLIENT.get_latest_blockhash().await?,
    );

    let vault_sig = SOLANA_CLIENT
        .send_and_confirm_transaction(&vault_tx)
        .await?;

    tracing::info!(
        "Created vaults for market. Token: {}, Coin vault: {}, PC vault: {}, Signature: {}",
        pc_mint.to_string(),
        coin_vault.0,
        pc_coin_vault.0,
        vault_sig
    );

    let market_ixs = create_serum_market_ixs(
        program_id,
        &payer.pubkey(),
        vault_signer_nonce,
        &market,
        &coin_vault,
        &pc_coin_vault,
        coin_mint,
        pc_mint,
        coin_lot_size,
        pc_lot_size,
    )
    .await?;
    let recent_block_hash = SOLANA_CLIENT.get_latest_blockhash().await?;
    let signers = [payer];

    let market_tx = Transaction::new_signed_with_payer(
        &market_ixs,
        Some(&payer.pubkey()),
        &signers,
        recent_block_hash,
    );
    let market_sig = SOLANA_CLIENT
        .send_and_confirm_transaction_with_spinner_and_config(
            &market_tx,
            CommitmentConfig::confirmed(),
            RpcSendTransactionConfig {
                skip_preflight: true,
                ..RpcSendTransactionConfig::default()
            },
        )
        .await?;
    tracing::info!(
        "Created market for token: {}, Market: {}, Signature: {}",
        pc_mint.to_string(),
        market.0,
        market_sig
    );

    Ok(market.0)
}

async fn create_vaults_ixs(
    wallet: &Pubkey,
    vault_owner: &Pubkey,
    coin_vault: &(Pubkey, String),
    pc_coin_vault: &(Pubkey, String),
    coin_mint: &Pubkey,
    pc_coin_mint: &Pubkey,
) -> anyhow::Result<Vec<Instruction>> {
    let account_lamports = SOLANA_CLIENT
        .get_minimum_balance_for_rent_exemption(165)
        .await?;
    Ok(vec![
        system_instruction::create_account_with_seed(
            wallet,
            &coin_vault.0,
            wallet,
            &coin_vault.1,
            account_lamports,
            165,
            &spl_token::id(),
        ),
        system_instruction::create_account_with_seed(
            wallet,
            &pc_coin_vault.0,
            wallet,
            &pc_coin_vault.1,
            account_lamports,
            165,
            &spl_token::id(),
        ),
        spl_token::instruction::initialize_account(
            &spl_token::id(),
            &coin_vault.0,
            coin_mint,
            vault_owner,
        )?,
        spl_token::instruction::initialize_account(
            &spl_token::id(),
            &pc_coin_vault.0,
            pc_coin_mint,
            vault_owner,
        )?,
    ])
}

fn generate_pubkey(from_pubkey: &Pubkey, program_id: &Pubkey) -> anyhow::Result<(Pubkey, String)> {
    let seed = format!("{:.32}", Keypair::new().pubkey().to_string());
    let public_key = Pubkey::create_with_seed(from_pubkey, &seed, program_id)?;
    Ok((public_key, seed))
}

const MAX_VAULT_NONCE_ATTEMPTS: u64 = 25555;

fn get_vault_owner_and_nonce(
    market_id: &Pubkey,
    program_id: &Pubkey,
) -> anyhow::Result<(Pubkey, u64)> {
    for nonce in 0..MAX_VAULT_NONCE_ATTEMPTS {
        if let Ok(vault_owner) =
            Pubkey::create_program_address(&[market_id.as_ref(), &nonce.to_le_bytes()], program_id)
        {
            return Ok((vault_owner, nonce));
        }
    }
    Err(anyhow::anyhow!(
        "Could not find valid vault owner after {} attempts",
        MAX_VAULT_NONCE_ATTEMPTS
    ))
}

#[allow(clippy::too_many_arguments)]
async fn create_serum_market_ixs(
    program_id: &Pubkey,
    payer_pubkey: &Pubkey,
    vault_signer_nonce: u64,
    market: &(Pubkey, String),
    coin_vault: &(Pubkey, String),
    pc_coin_vault: &(Pubkey, String),
    coin_mint: &Pubkey,
    pc_coin_mint: &Pubkey,
    coin_lot_size: u64,
    pc_lot_size: u64,
) -> anyhow::Result<Vec<Instruction>> {
    let request_queue = generate_pubkey(payer_pubkey, program_id)?;
    let event_queue = generate_pubkey(payer_pubkey, program_id)?;
    let bids = generate_pubkey(payer_pubkey, program_id)?;
    let asks = generate_pubkey(payer_pubkey, program_id)?;
    let fee_rate_bps = 0;
    let pc_dust_threshold = 100;

    let mut ixs = vec![
        system_instruction::create_account_with_seed(
            payer_pubkey,
            &market.0,
            payer_pubkey,
            &market.1,
            SOLANA_CLIENT
                .get_minimum_balance_for_rent_exemption(388)
                .await?,
            388,
            program_id,
        ),
        system_instruction::create_account_with_seed(
            payer_pubkey,
            &request_queue.0,
            payer_pubkey,
            &request_queue.1,
            SOLANA_CLIENT
                .get_minimum_balance_for_rent_exemption(764)
                .await?,
            764,
            program_id,
        ),
        system_instruction::create_account_with_seed(
            payer_pubkey,
            &event_queue.0,
            payer_pubkey,
            &event_queue.1,
            SOLANA_CLIENT
                .get_minimum_balance_for_rent_exemption(11308)
                .await?,
            11308,
            program_id,
        ),
        system_instruction::create_account_with_seed(
            payer_pubkey,
            &bids.0,
            payer_pubkey,
            &bids.1,
            SOLANA_CLIENT
                .get_minimum_balance_for_rent_exemption(14524)
                .await?,
            14524,
            program_id,
        ),
        system_instruction::create_account_with_seed(
            payer_pubkey,
            &asks.0,
            payer_pubkey,
            &asks.1,
            SOLANA_CLIENT
                .get_minimum_balance_for_rent_exemption(14524)
                .await?,
            14524,
            program_id,
        ),
    ];

    let accounts = vec![
        account_meta(market.0, false),
        account_meta(request_queue.0, false),
        account_meta(event_queue.0, false),
        account_meta(bids.0, false),
        account_meta(asks.0, false),
        account_meta(coin_vault.0, false),
        account_meta(pc_coin_vault.0, false),
        account_meta_readonly(*coin_mint, false),
        account_meta_readonly(*pc_coin_mint, false),
        account_meta_readonly(solana_sdk::sysvar::rent::id(), false),
    ];
    let ix_data = MarketInstruction::InitializeMarket(InitializeMarketInstruction {
        coin_lot_size,
        pc_lot_size,
        fee_rate_bps,
        vault_signer_nonce,
        pc_dust_threshold,
    });
    let data = ix_data.pack();
    let ix = Instruction {
        program_id: *program_id,
        accounts,
        data,
    };
    ixs.push(ix);
    Ok(ixs)
}

async fn gen_account_instr(
    client: &RpcClient,
    program_id: &Pubkey,
    payer: &Pubkey,
    key: &Pubkey,
    unpadded_len: usize,
) -> Result<Instruction> {
    let create_account_instr = solana_sdk::system_instruction::create_account(
        payer,
        key,
        client
            .get_minimum_balance_for_rent_exemption(unpadded_len)
            .await?,
        unpadded_len as u64,
        program_id,
    );
    Ok(create_account_instr)
}

fn account_meta(pubkey: Pubkey, is_signer: bool) -> AccountMeta {
    AccountMeta {
        pubkey,
        is_signer,
        is_writable: true,
    }
}

fn account_meta_readonly(pubkey: Pubkey, is_signer: bool) -> AccountMeta {
    AccountMeta {
        pubkey,
        is_signer,
        is_writable: false,
    }
}
