use anyhow::Result;
use solana_sdk::{pubkey::Pubkey, signature::Keypair, signer::Signer};

use super::amm_types::AmmKeys;

pub fn get_amm_pda_keys(
    amm_program: &Pubkey,
    market_program: &Pubkey,
    market: &Pubkey,
    coin_mint: &Pubkey,
    pc_mint: &Pubkey,
) -> Result<AmmKeys> {
    let amm_pool = raydium_amm::processor::get_associated_address_and_bump_seed(
        amm_program,
        market,
        raydium_amm::processor::AMM_ASSOCIATED_SEED,
        amm_program,
    )
    .0;
    let (amm_authority, nonce) =
        Pubkey::find_program_address(&[raydium_amm::processor::AUTHORITY_AMM], amm_program);
    let amm_open_order = raydium_amm::processor::get_associated_address_and_bump_seed(
        amm_program,
        market,
        raydium_amm::processor::OPEN_ORDER_ASSOCIATED_SEED,
        amm_program,
    )
    .0;
    let amm_lp_mint = raydium_amm::processor::get_associated_address_and_bump_seed(
        amm_program,
        market,
        raydium_amm::processor::LP_MINT_ASSOCIATED_SEED,
        amm_program,
    )
    .0;
    let amm_coin_vault = raydium_amm::processor::get_associated_address_and_bump_seed(
        amm_program,
        market,
        raydium_amm::processor::COIN_VAULT_ASSOCIATED_SEED,
        amm_program,
    )
    .0;
    let amm_pc_vault = raydium_amm::processor::get_associated_address_and_bump_seed(
        amm_program,
        market,
        raydium_amm::processor::PC_VAULT_ASSOCIATED_SEED,
        amm_program,
    )
    .0;
    let amm_target = raydium_amm::processor::get_associated_address_and_bump_seed(
        amm_program,
        market,
        raydium_amm::processor::TARGET_ASSOCIATED_SEED,
        amm_program,
    )
    .0;

    Ok(AmmKeys {
        amm_pool,
        amm_target,
        amm_coin_vault,
        amm_pc_vault,
        amm_lp_mint,
        amm_open_order,
        amm_coin_mint: *coin_mint,
        amm_pc_mint: *pc_mint,
        amm_authority,
        market: *market,
        market_program: *market_program,
        nonce,
    })
}

pub fn generate_pubkey(
    from_pubkey: &Pubkey,
    program_id: &Pubkey,
) -> anyhow::Result<(Pubkey, String)> {
    let seed = format!("{:.32}", Keypair::new().pubkey().to_string());
    let public_key = Pubkey::create_with_seed(from_pubkey, &seed, program_id)?;
    Ok((public_key, seed))
}
