use anyhow::Error;
use bigdecimal::BigDecimal;
use bson::Decimal128;
use solana_program::program_pack::Pack;
use solana_sdk::{
    instruction::Instruction, pubkey::Pubkey, signature::Keypair, signer::Signer,
    system_instruction,
};
use spl_token::state::{Account, Mint};
use std::str::FromStr;

use crate::{
    constants::SOLANA_DECIMALS,
    shared::{SOLANA_CLIENT, WSOL_PUBKEY},
};
const DEFAULT_TOKEN_DECIMAL: u8 = 6;

use super::amm_utils::generate_pubkey;

pub async fn get_token_decimals(mint_pubkey: &Pubkey) -> anyhow::Result<u8> {
    let mint_account = match SOLANA_CLIENT.get_account(mint_pubkey).await {
        Ok(account) => {
            tracing::info!(
                "Successfully fetched account data for mint: {}",
                mint_pubkey
            );
            account
        }
        Err(err) => {
            tracing::error!(
                "Failed to fetch account for mint {}: {:?}",
                mint_pubkey,
                err
            );
            return Ok(DEFAULT_TOKEN_DECIMAL);
        }
    };
    let mint = Mint::unpack(&mint_account.data)
        .map_err(|e| anyhow::anyhow!("Failed to unpack mint: {}", e))?;
    Ok(mint.decimals)
}

pub fn add_decimal128(a: Decimal128, b: Decimal128) -> Decimal128 {
    // Convert Decimal128 to f64 for arithmetic
    let a_f64 = a.to_string().parse::<f64>().unwrap_or(0.0);
    let b_f64 = b.to_string().parse::<f64>().unwrap_or(0.0);

    // Perform the addition
    let result_f64 = a_f64 + b_f64;

    // Convert the result back to Decimal128
    Decimal128::from_str(&result_f64.to_string()).unwrap()
}

pub fn min_decimal128(a: Decimal128, b: Decimal128) -> Decimal128 {
    Decimal128::from_str(
        &a.to_string()
            .parse::<f64>()
            .unwrap()
            .min(b.to_string().parse::<f64>().unwrap())
            .to_string(),
    )
    .unwrap()
}

pub fn max_decimal128(a: Decimal128, b: Decimal128) -> Decimal128 {
    Decimal128::from_str(
        &a.to_string()
            .parse::<f64>()
            .unwrap()
            .max(b.to_string().parse::<f64>().unwrap())
            .to_string(),
    )
    .unwrap()
}

pub trait ToF64 {
    fn to_f64(&self) -> f64;
}

impl ToF64 for Decimal128 {
    fn to_f64(&self) -> f64 {
        self.to_string().parse::<f64>().unwrap_or_else(|e| {
            tracing::error!("Failed to parse Decimal128 to f64: {}", e);
            0.0
        })
    }
}

impl ToF64 for BigDecimal {
    fn to_f64(&self) -> f64 {
        self.to_string().parse::<f64>().unwrap_or_else(|e| {
            tracing::error!("Failed to parse BigDecimal to f64: {}", e);
            0.0
        })
    }
}

pub fn get_market_cap_sol(
    virtual_token_reserves: u64,
    virtual_sol_reserves: u64,
    token_total_supply: u64,
) -> f64 {
    if virtual_token_reserves == 0 {
        0.0
    } else {
        let bg_virtual_sol_reserves =
            BigDecimal::from_str(&virtual_sol_reserves.to_string()).unwrap();
        let bg_virtual_token_reserves =
            BigDecimal::from_str(&virtual_token_reserves.to_string()).unwrap();
        let bg_token_total_supply = BigDecimal::from_str(&token_total_supply.to_string()).unwrap();
        let bg_mcap = bg_virtual_sol_reserves / bg_virtual_token_reserves * bg_token_total_supply;

        un_decimals_from_str(&bg_mcap.to_string(), SOLANA_DECIMALS as u32)
    }
}

pub fn calculate_bonding_curve_progress(
    real_token_reserves: u64,
    initial_real_token_reserves: u64,
) -> f64 {
    let bonding_progress = if real_token_reserves >= initial_real_token_reserves {
        0.0
    } else {
        1.0 - (real_token_reserves as f64 / initial_real_token_reserves as f64)
    };

    // round the number off to 4 decimal places
    (bonding_progress * 1e6).floor() / 1e4
}

pub fn load_solana_keypair_from_private_key(private_key: &str) -> Keypair {
    Keypair::from_base58_string(private_key)
}

pub fn un_decimals_from_str(number: &str, decimal: u32) -> f64 {
    let bg_number = BigDecimal::from_str(number).unwrap();
    (bg_number / BigDecimal::from(10i64.pow(decimal))).to_f64()
}

pub fn create_wsol_account(
    wallet: &Keypair,
    amount: u64,
) -> Result<(Pubkey, Vec<Instruction>), Error> {
    let (wsol_pubkey, seed) = generate_pubkey(&wallet.pubkey(), &spl_token::ID)?;

    let instructions: Vec<Instruction> = vec![
        system_instruction::create_account_with_seed(
            &wallet.pubkey(),
            &wsol_pubkey,
            &wallet.pubkey(),
            &seed,
            amount,
            Account::LEN as u64,
            &spl_token::ID,
        ),
        spl_token::instruction::initialize_account(
            &spl_token::ID,
            &wsol_pubkey,
            &WSOL_PUBKEY,
            &wallet.pubkey(),
        )?,
    ];
    Ok((wsol_pubkey, instructions))
}

pub fn close_wsol_account(wallet: &Keypair, wsol_pubkey: &Pubkey) -> Result<Instruction, Error> {
    Ok(spl_token::instruction::close_account(
        &spl_token::ID,
        wsol_pubkey,
        &wallet.pubkey(),
        &wallet.pubkey(),
        &[],
    )?)
}
