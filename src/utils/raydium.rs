use anchor_lang::AccountDeserialize;
use solana_client::nonblocking::rpc_client::RpcClient;
use solana_sdk::{account::Account, commitment_config::CommitmentConfig, pubkey::Pubkey};
use spl_token_2022::extension::{
    transfer_fee::{TransferFeeConfig, MAX_FEE_BASIS_POINTS},
    BaseState, BaseStateWithExtensions, StateWithExtensions,
};

use super::clmm_utils::unpack_mint;

pub async fn get_anchor_account<T: AccountDeserialize>(
    client: &RpcClient,
    addr: &Pubkey,
) -> Result<Option<T>, anyhow::Error> {
    if let Some(account) = client
        .get_account_with_commitment(addr, CommitmentConfig::processed())
        .await?
        .value
    {
        let mut data: &[u8] = &account.data;
        let ret = T::try_deserialize(&mut data).unwrap();
        Ok(Some(ret))
    } else {
        Ok(None)
    }
}

pub const TEN_THOUSAND: u128 = 10000;

#[derive(Debug)]
pub struct TransferFeeInfo {
    pub mint: Pubkey,
    pub owner: Pubkey,
    pub transfer_fee: u64,
}

pub fn amount_with_slippage(
    amount: u64,
    slippage_bps: u64,
    up_towards: bool,
) -> Result<u64, anyhow::Error> {
    let amount = amount as u128;
    let slippage_bps = slippage_bps as u128;
    let amount_with_slippage = if up_towards {
        amount
            .checked_mul(slippage_bps.checked_add(TEN_THOUSAND).unwrap())
            .unwrap()
            .checked_div(TEN_THOUSAND)
            .unwrap()
    } else {
        amount
            .checked_mul(TEN_THOUSAND.checked_sub(slippage_bps).unwrap())
            .unwrap()
            .checked_div(TEN_THOUSAND)
            .unwrap()
    };
    u64::try_from(amount_with_slippage)
        .map_err(|_| anyhow::anyhow!("failed to read keypair from {}", amount_with_slippage))
}

pub async fn get_pool_mints_inverse_fee(
    rpc_client: &RpcClient,
    token_mint_0: Pubkey,
    token_mint_1: Pubkey,
    post_fee_amount_0: u64,
    post_fee_amount_1: u64,
) -> (TransferFeeInfo, TransferFeeInfo) {
    let load_accounts = vec![token_mint_0, token_mint_1];
    let rsps = rpc_client
        .get_multiple_accounts(&load_accounts)
        .await
        .unwrap();
    let epoch = rpc_client.get_epoch_info().await.unwrap().epoch;
    let mint0_account = rsps[0].clone().ok_or("load mint0 rps error!").unwrap();
    let mint1_account = rsps[1].clone().ok_or("load mint0 rps error!").unwrap();
    let mint0_state = unpack_mint(&mint0_account.data).unwrap();
    let mint1_state = unpack_mint(&mint1_account.data).unwrap();
    (
        TransferFeeInfo {
            mint: token_mint_0,
            owner: mint0_account.owner,
            transfer_fee: get_transfer_inverse_fee(&mint0_state, post_fee_amount_0, epoch),
        },
        TransferFeeInfo {
            mint: token_mint_1,
            owner: mint1_account.owner,
            transfer_fee: get_transfer_inverse_fee(&mint1_state, post_fee_amount_1, epoch),
        },
    )
}

pub fn get_transfer_inverse_fee<S: BaseState>(
    account_state: &StateWithExtensions<'_, S>,
    epoch: u64,
    post_fee_amount: u64,
) -> u64 {
    let fee = if let Ok(transfer_fee_config) = account_state.get_extension::<TransferFeeConfig>() {
        let transfer_fee = transfer_fee_config.get_epoch_fee(epoch);
        if u16::from(transfer_fee.transfer_fee_basis_points) == MAX_FEE_BASIS_POINTS {
            u64::from(transfer_fee.maximum_fee)
        } else {
            transfer_fee_config
                .calculate_inverse_epoch_fee(epoch, post_fee_amount)
                .unwrap()
        }
    } else {
        0
    };
    fee
}

pub fn deserialize_anchor_account<T: AccountDeserialize>(
    account: &Account,
) -> Result<T, anyhow::Error> {
    let mut data: &[u8] = &account.data;
    T::try_deserialize(&mut data).map_err(Into::into)
}
