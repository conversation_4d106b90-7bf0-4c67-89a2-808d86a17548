use serde::{Deserialize, Serialize};

use crate::db::trades::ETradeType;

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct PubTradesData {
    pub slot: u64,
    pub hash: String,
    pub token_address: String,
    pub index: u64,
    pub maker: String,
    pub trade_type: ETradeType,
    pub base_amount: String,
    pub quote_amount: String,
    pub price: String,
    pub price_usd: String,
    pub volume: String,
    pub volume_usd: String,
    pub timestamp: u64,
    pub virtual_sol_reserves: String,
    pub virtual_token_reserves: String,
    pub real_sol_reserves: String,
    pub real_token_reserves: String,
    pub token_symbol: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct PubCreatedCoins {
    pub token_address: String,
    pub bonding_curve_address: String,
    pub name: String,
    pub symbol: String,
    pub logo_uri: String,
    pub creator_address: String,
    pub virtual_sol_reserves: u64,
    pub virtual_token_reserves: u64,
    pub real_token_reserves: u64,
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct PubUpdatedCoins {
    pub mcap: f64,
    pub mcap_usd: f64,
    pub virtual_sol_reserves: u64,
    pub virtual_token_reserves: u64,
    pub real_sol_reserves: u64,
    pub real_token_reserves: u64,
    pub bonding_curve_progress: f64,
    pub token_address: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "camelCase")]
pub struct PubHolders {
    pub user_address: String,
    pub change_amount: f64,
    pub last_hash: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub last_synced_at: Option<i64>,
}
